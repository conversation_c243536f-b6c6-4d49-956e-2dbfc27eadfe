import json
import cherrypy
import re
import base64
from wechatpy.enterprise import WeChatClient
from http.cookies import Simple<PERSON><PERSON><PERSON>
from config import MyConfig
from logger.logger import basic_log, log, msg_log
from saml2 import time_util
 
import time
from saml2.s_utils import rndstr
from urllib.parse import parse_qs
from saml2.httputil import Redirect
from utility import cookie
from utility.ip import allow_ip, get_real_loc, get_remote_ip




class work_wechat:
    def __init__(self):
        ##wx3f2b0e0abc129517 corpId   appid 1000004
        my_config = MyConfig().config
        self.app_id = my_config["WeWork"]["app_id"]
        self.app_secret = my_config["WeWork"]["app_secret"]
        self.my_config = my_config
        self.client = WeChatClient(self.app_id, self.app_secret)
        self.jsapi_utils = self.client

    def handle_api_request(self, path, environ, start_response, IDP):

        if path == "sso/api/work/get_jsapi_ticket.*":
            ticket_data = self.jsapi_utils.get_jsapi_ticket()
            return json_response(ticket_data, start_response)
        elif path == "sso/api/work/get_jsapi_card_ticket.*":
            card_ticket_data = self.jsapi_utils.get_jsapi_card_ticket()
            return json_response(card_ticket_data, start_response)
        elif path.startswith("sso/api/work/get_jsapi_card_params.*"):
            query_string = environ.get("QUERY_STRING", "")
            params = parse_qs(query_string)
            card_ticket = params.get("card_ticket", [""])[0]
            card_type = params.get("card_type", [""])[0]
            kwargs = {}  # 构建参数字典
            params_data = self.jsapi_utils.get_jsapi_card_params(
                card_ticket, card_type, **kwargs
            )
            return json_response(params_data, start_response)
        elif path.startswith("sso/api/work/get_jsapi_signature.*"):
            query_string = environ.get("QUERY_STRING", "")
            params = parse_qs(query_string)
            noncestr = params.get("noncestr", [""])[0]
            ticket = params.get("ticket", [""])[0]
            timestamp = params.get("timestamp", [""])[0]
          
            url = params.get("url", [""])[0]
            signature_data = self.jsapi_utils.get_jsapi_signature(
                noncestr, ticket, timestamp, url
            )
            return json_response(signature_data, start_response)
        elif path.startswith("sso/api/work/login"):
            query_string = environ.get("QUERY_STRING", "")
            params = parse_qs(query_string)
            code = params.get("code", [""])[0]
            remember_text=params.get("srm",[""])[0]
            loginfo= basic_log(environ,"Login")
            if code:
                isWhite= allow_ip(environ)
                loc= get_real_loc(environ)
                if not isWhite:
                    remember_text=""
                user_info = self.client.oauth.get_user_info(code)
                if user_info:
                    my_config = self.my_config
                    uid = rndstr(24)                   
                    user = user_info["UserId"]
                    if "@" not in user:
                        user = user +"@"+  my_config["Auth"]["domain"]
                    try:
                        
                        mapperUser = my_config["WorkToUserMapper"][user]
                    except KeyError:
                        # 处理键错误的情况，例如给 mapperUser 赋予一个默认值
                        mapperUser = None  # 或者任何你认为合适的默认值

                    if mapperUser:
                        user = mapperUser
                        # 记录用户映射日志
                        logger = log()
                        logger.warning("User %s is mapped to %s", user_info["UserId"], user)
                    ip= get_remote_ip( environ)
                    agent= environ['HTTP_USER_AGENT']
                    IDP.session_manager.add_session(uid, user, ip, remember_text,"qywork",agent,loc,isWhite)
                    from cache.user_cache import user_login_success
                    user_login_success(user_info["UserId"],ip,loc) 
                    timeout=None
                    if remember_text=="1":
                        timeout= int(MyConfig().get("SessionManager","remember_timeout",30))
                    kaka = cookie.set_cookie(
                        "idpauthn", timeout, "/", uid, params["authn_reference"][0]
                    )
                    # 20250613 添加ip_web 登录成功记录

                    lox = f"{params['redirect_uri'][0]}?id={uid}&key={params['key'][0]}"
                    logger = log()
                    loginfo["user"]=user
                    loginfo["username"]=user
                    loginfo["login_status"]=True
                    loginfo["login_type"]="qywork"
                    loginfo["callback"]=lox
                    logger.info(json.dumps( loginfo))
                    environ["user"]=loginfo
                    logger.debug("Redirect => %s", lox)
                    resp = Redirect(lox, headers=[kaka], content="text/html")
                    return resp(environ, start_response)
            
                logger = log()
                loginfo["user"]=user
                loginfo["login_status"]=False
                loginfo["login_type"]="qywork"
                loginfo["callback"]=lox
                loginfo["msg"]=user_info
                logger.info(json.dumps( loginfo))
                    

        else:
            start_response("404 Not Found", [("Content-Type", "text/plain")])
            return [b"Not Found api"]


def json_response(data, start_response):
    start_response("200 OK", [("Content-Type", "application/json")])
    return [json.dumps(data).encode()]

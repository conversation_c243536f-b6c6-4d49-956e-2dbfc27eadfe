#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os.path

from logger.sensitive_data_filter import SensitiveDataFilter
from logger.sso_logstash_formatter import SSOLogstashFormatter
from saml2 import BINDING_HTTP_ARTIFACT
from saml2 import BINDING_HTTP_POST
from saml2 import BINDING_HTTP_REDIRECT
from saml2 import BINDING_SOAP
from saml2 import BINDING_URI
from saml2.saml import NAME_FORMAT_URI
from saml2.saml import NAMEID_FORMAT_PERSISTENT
from saml2.saml import NAMEID_FORMAT_TRANSIENT
from saml2.xmldsig import SIG_RSA_SHA256, DIGEST_SHA256

import logging 
import logging.config
from logstash_async.formatter import LogstashFormatter
from logstash_async.constants import constants

# constants.SOCKET_CLOSE_WAIT_TIMEOUT = 10


try:
    from saml2.sigver import get_xmlsec_binary
except ImportError:
    get_xmlsec_binary = None

if get_xmlsec_binary:
    xmlsec_path = get_xmlsec_binary(["/opt/local/bin"])
else:
    xmlsec_path = "/usr/bin/xmlsec1"

BASEDIR = os.path.abspath(os.path.dirname(__file__))


def full_path(local_file):
    return os.path.join(BASEDIR, local_file)

LogAccess=True
HOST = "www.shecc.com"
PORT = 443

HTTPS = True

if HTTPS:
    BASE = "https://%s:%s" % (HOST, PORT)
else:
    BASE = "http://%s:%s" % (HOST, PORT)

# HTTPS cert information
SERVER_CERT = "certs/shecc.pem"
SERVER_KEY = "certs/shecckey.pem"
CERT_CHAIN = ""
# SIGN_ALG = None
# DIGEST_ALG = None
# SIGN_ALG = ds.SIG_RSA_SHA512
# DIGEST_ALG = ds.DIGEST_SHA512


CONFIG = {
    "entityid": "%s/idp.xml" % BASE,
    "description": "电科数字IDP",
    # "valid_for": 168,
    "service": {
        "aa": {
            "endpoints": {"attribute_service": [("%s/attr" % BASE, BINDING_SOAP)]},
            "name_id_format": [NAMEID_FORMAT_TRANSIENT, NAMEID_FORMAT_PERSISTENT],
        },
        "aq": {
            "endpoints": {"authn_query_service": [("%s/aqs" % BASE, BINDING_SOAP)]},
        },
        "idp": {
            "name": "Shecc IdP",
            "endpoints": {
                "single_sign_on_service": [
                    ("%s/sso/redirect" % BASE, BINDING_HTTP_REDIRECT),
                    ("%s/sso/post" % BASE, BINDING_HTTP_POST),
                    ("%s/sso/art" % BASE, BINDING_HTTP_ARTIFACT),
                    ("%s/sso/ecp" % BASE, BINDING_SOAP),
                ],
                "single_logout_service": [
                    ("%s/slo/soap" % BASE, BINDING_SOAP),
                    ("%s/slo/post" % BASE, BINDING_HTTP_POST),
                    ("%s/slo/redirect" % BASE, BINDING_HTTP_REDIRECT),
                ],
                "artifact_resolve_service": [("%s/ars" % BASE, BINDING_SOAP)],
                "assertion_id_request_service": [("%s/airs" % BASE, BINDING_URI)],
                "manage_name_id_service": [
                    ("%s/mni/soap" % BASE, BINDING_SOAP),
                    ("%s/mni/post" % BASE, BINDING_HTTP_POST),
                    ("%s/mni/redirect" % BASE, BINDING_HTTP_REDIRECT),
                    ("%s/mni/art" % BASE, BINDING_HTTP_ARTIFACT),
                ],
                "name_id_mapping_service": [
                    ("%s/nim" % BASE, BINDING_SOAP),
                ],
            },
            "policy": {
                "default": {
                    "lifetime": {"minutes": 15},
                    "attribute_restrictions": None,  # means all I have
                    "name_form": "urn:oasis:names:tc:SAML:2.0:attrname-format:unspecified",
                    # "entity_categories": ["swamid", "edugain"]
                },
            },
            "subject_data": "./idp.subject",
            "name_id_format": [
                "urn:oasis:names:tc:SAML:2.0:attrname-format:unspecified"
            ],
            "sign_assertion": True,
            "sign_response": True,
            "signing_algorithm": "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256",
            "digest_algorithm": "http://www.w3.org/2001/04/xmlenc#sha256",
            #     "only_use_keys_in_metadata": False,
            "validate_certificate": True,
            "want_authn_requests_signed": False,
            #    "want_authn_requests_only_with_valid_cert":False
        },
    },
    "debug": 0,
    "key_file": full_path("certs/adfs.key"),
    "cert_file": full_path("certs/adfs-cert.pem"),
    "metadata": {
        "local": [full_path("pki/sp.xml")],
    },
    # "encryption_keypairs": [
    #     {
    #         "key_file": full_path("configs/certs/adfs.key"),
    #         "cert_file": full_path("configs/certs/adfs-cert.pem"),
    #     },
    # ],
    "organization": {
        "display_name": "电科数字",
        "name": "Shecc",
        "url": "http://www.shecc.com",
    },
    "contact_person": [
        {
            "contact_type": "technical",
            "given_name": "lvjw",
            "email_address": "<EMAIL>",
        },
        {
            "contact_type": "support",
            "given_name": "Support",
            "email_address": "<EMAIL>",
        },
    ],
    # This database holds the map between a subject's local identifier and
    # the identifier returned to a SP
    "xmlsec_binary": xmlsec_path,
    # "attribute_map_dir": "./attributemaps",
    "attribute_map_dir": "./saml2Config",
    "logging": {
        "version": 1,
        "disable_existing_loggers": False,  #  不禁用现有的日志记录器
        "formatters": {
            "simple": { 
                "()": "logger.logger.CustomFormatter",
                "format": "[%(asctime)s] [%(levelname)s] [%(name)s.%(funcName)s]  [%(current_user)s] [%(pre_user)s] [%(ip)s] [%(remote)s] [%(request_path)s] [%(request_method)s] [%(request_port)s] [%(trace_id)s] [%(user_agent)s] %(message)s",
            },
            "logstash": {
                "()": SSOLogstashFormatter,
                "message_type": "logstash",
                "fqdn": False,
                "ensure_ascii": False,
                "extra": {
                    "environment": "adfs",
                    "ip": "%(ip)s",
                    "remote": "%(remote)s",
                    "request_path": "%(request_path)s",
                    "request_method": "%(request_method)s",
                    "request_port": "%(request_port)s",
                    "trace_id": "%(trace_id)s",
                         "pre_user": "%(pre_user)s",
                    "user_agent": "%(user_agent)s",
                    "current_user": "%(current_user)s",
                    "request_url": "%(request_url)s",
                    "referer": "%(referer)s",
                    "content_type": "%(content_type)s",
                    "content_length": "%(content_length)s"
                },
            },
        },
        "filters": {
            "sensitive_filter": {
                "()": SensitiveDataFilter,
            },
        },
        "handlers": {
            "stderr": {
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stderr",
                "level": "INFO",
                "formatter": "simple",
               
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": "logs/idp.log",  
                "backupCount": 200,
                "maxBytes": 5242880,  # 5MB
                "level": "INFO",
                "formatter": "simple",
                "encoding": "utf-8"
            },
            "logstash": {
                "class": "logstash_async.handler.AsynchronousLogstashHandler",
                #   "transport": "logstash_async.transport.UdpTransport",
                "host": "***********",  # Logstash服务器的地址
                "port": 50000,  # Logstash服务器的端口
                "level": "INFO",
                "formatter": "logstash",
                "database_path": "logs/logstash.db",  # "logs/logstash.db"  # 本地数据库路径
                "ensure_ascii": False,  # 添加编码设置
             
            },
        },
        "loggers": {
            "": {"level": "INFO", "handlers": ["stderr", "file","logstash"]},
            # "saml2": {
            #     "level": "DEBUG",
            #     "handlers": ["stderr", "file", "logstash"],
            # },
        },
        # "root": {
        #     "level": "DEBUG",
        #     "handlers": ["stderr", "file", "logstash"],
        # },
    },
    
}

# Authentication contexts

# (r'verify?(.*)$', do_verify),

# CAS_SERVER = "https://cas.umu.se"
# CAS_VERIFY = "%s/verify_cas" % BASE
# PWD_VERIFY = "%s/verify_pwd" % BASE

# AUTHORIZATION = {
#     "CAS" : {"ACR": "CAS", "WEIGHT": 1, "URL": CAS_VERIFY},
#     "UserPassword" : {"ACR": "PASSWORD", "WEIGHT": 2, "URL": PWD_VERIFY}
# }

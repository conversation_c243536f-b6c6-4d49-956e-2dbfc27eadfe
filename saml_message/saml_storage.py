
import json
from saml_message.saml_message import SAMLMessage
from config import MyConfig

class SAMLStorage:
    def __init__(self, redis_client):
        self.redis_client = redis_client
        config=MyConfig().config
        self.expiration =30
        if "SessionManager" in config:
            if "saml_timeout" in config["SessionManager"]:                
                timeout=  int(config["SessionManager"]["saml_timeout"])
                self.expiration= timeout
        
    def store_saml_message(self, key, saml_message):
        key="saml:"+key
        # 将对象转换为 JSON 字符串并存储到 Redis
        self.redis_client.set(key, json.dumps(saml_message.to_dict()), 60*self.expiration)

    def get_saml_message(self, key):
        key="saml:"+key
        if not self.redis_client.exists(key):
            return None
        saml_msg_json = self.redis_client.get(key)
        if saml_msg_json is None:
            return None

        saml_msg_dict = json.loads(saml_msg_json)
        return saml_msg_dict
    def del_saml_message(self,key):
        key="saml:"+key
        self.redis_client.delete(key)

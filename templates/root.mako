<% self.seen_css=set() %>
    <%def name="css_link(path, media='')" filter="trim">
        % if path not in self.seen_css:
        <link rel="stylesheet" type="text/css" href="${path|h}" media="${media}">
        % endif
        <% self.seen_css.add(path) %>
    </%def>
    <%def name="css()" filter="trim">
        ${css_link('/static/css/main.css', 'screen')}
    </%def>
    
    <%def name="post()" filter="trim">
        <div>
            <div class="footer">
                <p>&#169; Copyright 2024 电科数字</p>
            </div>
        </div>
    </%def>
    ##
    <!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN " ##"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
    <html>

    <head>

        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <script src="/sso/static/js/jquery.min.js"></script>
        <script src="/sso/static/js/axios.min.js"></script> 
        <%block name="header"></%block>
    </head>

    <body>       
        ${next.body()}
    </body>

    </html>
import re
import configparser
from pathlib import Path

def extract_username(account: str) -> str:
        """
        从各种格式的用户名中提取出真正的账号名
        :param account: 用户名字符串，可能是 '<EMAIL>'、'shecc\abc'、'shecc.com\abc'、'shecc\<EMAIL>' 格式
        :return: 提取后的用户名，例如 'abc'
        """
        # Step 1: 优先处理反斜杠 '\' 的情况（如果有）
        if '\\' in account:
            # 例如 'shecc\<EMAIL>' -> 取出 '\' 后的部分变成 '<EMAIL>'
            account = account.split('\\')[-1]

        # Step 2: 处理 '@' 符号的情况（如果有）
        if '@' in account:
            # 例如 '<EMAIL>' -> 提取 '@' 前的部分 'abc'
            account = account.split('@')[0]

        # 返回小写的账号名
        return account.lower()
class PasswordValidator:
    def __init__(self, account: str):
        """
        初始化密码验证器
        :param account: 用户的账号名 
        """
        # 加载规则配置文件
        self.config = configparser.ConfigParser()
        config_path = Path(__file__).parent / "password_rules.ini"
        self.config.read(config_path)
        
        # 加载弱密码字典
        dict_path = Path(__file__).parent / "password_dict.txt"
        with open(dict_path, 'r', encoding='utf-8') as f:
            self.weak_passwords = [line.strip() for line in f if line.strip()]
        
        self.account = extract_username(account.lower())
        self.forbidden_substrings = (
            self.config['Forbidden']['patterns'].split(',') + 
            [self.account] + 
            self.weak_passwords
        )
        
        # 从配置获取其他参数
        self.sequential_threshold = int(self.config['Sequential']['threshold'])
        self.repeated_char_limit = int(self.config['Repeated']['limit'])
        self.keyboard_patterns = self.config['Keyboard']['patterns'].split(',')

    def is_sequential(self, password: str) -> bool:
        """
        检查密码中是否包含连续字符或键盘相邻字符模式
        """
        # 检查连续递增或递减的字符序列
        for i in range(len(password) -  self.sequential_threshold  + 1):
            # 递增顺序
            if all(ord(password[i+j]) == ord(password[i]) + j for j in range( self.sequential_threshold )):
                return True
            # 递减顺序
            if all(ord(password[i+j]) == ord(password[i]) - j for j in range( self.sequential_threshold )):
                return True
        
        # 检查键盘相邻字符模式
        for pattern in self.keyboard_patterns:
            if password in pattern or password[::-1] in pattern:
                return True
        return False

    def has_repeated_chars(self, password: str) -> bool:
        """
        检查密码中是否包含6位及以上的相同字符
        """
        repeated_pattern = r"(.)\1{" + str(self.repeated_char_limit - 1) + r",}"  # 动态生成正则模式
        return re.search(repeated_pattern, password) is not None

    def contains_forbidden(self, password: str) -> bool:
        """
        检查密码中是否包含禁止的词汇
        """
        for forbidden in self.forbidden_substrings:
            if forbidden in password:
                return True
        return False

    def contains_account(self, password: str) -> bool:
        """
        检查密码中是否包含账号名称
        """
        return self.account in password

    def validate(self, password: str) -> (bool, str):
        """
        综合验证密码是否符合要求
        :param password: 待验证的密码
        :return: (验证结果, 错误信息)
        """
        password_lower = password.lower()
        
        # 检查连续字符模式
        if self.is_sequential(password_lower):
            return False, "密码包含连续或键盘相邻字符模式"
            
        # 检查禁用词汇
        for forbidden in self.forbidden_substrings:
            if forbidden in password_lower:
                if forbidden == self.account:
                    return False, f"密码中包含了账号名称 '{self.account}'"
                elif forbidden in self.weak_passwords:
                    return False, "密码属于常见弱密码"
                else:
                    return False, f"密码中包含禁用词汇 '{forbidden}'"
        
        # 检查重复字符
        if self.has_repeated_chars(password):
            return False, f"密码中包含{self.repeated_char_limit}位及以上相同字符"
            
        return True, "密码验证通过"

# 测试用例
if __name__ == "__main__":
    account = "user123"
    name_short = "xy"  # 假设姓名简拼是 xy

    validator = PasswordValidator(account)  # 创建密码验证器实例

    passwords = [
        "shecc123",         # 包含禁用词
        "abcdefg",          # 连续字符
        "user123xyz",       # 包含账号名
        "asdfghjkl",        # 键盘相邻
        "1qaz2wsx",         # 键盘模式
        "111111",           # 6位重复字符
        "aaaaaaa",          # 7位重复字符
        "12qwas",          # 7位重复字符
        "normalPass12"     # 合格密码
    ]

    for pwd in passwords:
        print(f"密码: {pwd} -> 结果: {validator.validate(pwd)}")

from threading import local

_thread_local = local()

def set_request_context(environ):
    """设置当前请求的上下文"""
    _thread_local.environ = environ
    
def get_request_context():
    """获取当前请求的上下文"""
    return getattr(_thread_local, 'environ', {})
    
# 上下文管理器
class RequestContext:
    def __init__(self, environ):
        self.environ = environ
        
    def __enter__(self):
        set_request_context(self.environ)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 清理上下文
        if hasattr(_thread_local, 'environ'):
            delattr(_thread_local, 'environ')
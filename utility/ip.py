import ipaddress

from config import MyConfig

#获取内部IP配置信息
def allow_ip(env):
    my_config = MyConfig()
    ip_whitelist = my_config.get("SessionManager", "ip_whitelist", "")
    ip = get_request_ip_noport(environ=env)
    isWhite = is_ip_in_whitelist(ip, ip_whitelist)
    return isWhite

#获取远程IP带端口
def get_request_remote(environ):
    return get_remote_ip(environ)
def get_request_ip_noport(environ):
    ip = ""
    if "HTTP_X_REAL_IP" in environ:
        ip = environ["HTTP_X_REAL_IP"]
    if (not ip or ip=="") and "X-Real-Ip" in environ:
        ip = environ["X-Real-Ip"]
    if (not ip or ip=="") and "HTTP_X_FORWARDED_FOR" in environ:
        ip = environ["HTTP_X_FORWARDED_FOR"]
    if (not ip or ip=="") and "REMOTE_ADDR" in environ:
        ip = environ["REMOTE_ADDR"]
    if ":" in ip:
        ip = ip.split(":")[0]
    return ip
def get_request_ip(environ):
    ip = ""
    if "REMOTE_ADDR" in environ:
        ip = environ["REMOTE_ADDR"]
    if "REMOTE_PORT" in environ:
        ip +=(":"+ environ["REMOTE_PORT"] )
    return ip
#获取远程IP不带端口

def get_remote_ip(environ):
    ip = ""
    if "HTTP_X_REAL_IP" in environ:
        ip = environ["HTTP_X_REAL_IP"]
    if "X-Real-Ip" in environ:
        ip = environ["X-Real-Ip"]
    if "HTTP_X_FORWARDED_FOR" in environ:
        ip = environ["HTTP_X_FORWARDED_FOR"]
    if ip == "":
        ip = get_request_ip(environ)
    ip = ip.split(":")[0]
    return ip

#获取内部追踪ID
def get_trace_id(environ):
    trace_id = ""
    if "trace_id" in environ:
        trace_id = environ["trace_id"]
    return trace_id

#获取LOC
def get_real_loc(environ):
    loc = ""
    if "HTTP_X_REAL_LOC" in environ:
        loc = environ["HTTP_X_REAL_LOC"]
        loc = decode_unicode_escape(loc).replace('"', "")
    return loc

#获取
def decode_unicode_escape(escaped_str):
    try:

        # 将包含 Unicode 转义序列的字符串解码为实际的 Unicode 字符
        unicode_str = escaped_str.encode().decode("unicode_escape")
        return unicode_str
    except Exception as e:
        return ""


def get_user_agent(environ):
    agent = ""
    if "HTTP_USER_AGENT" in environ:
        agent = environ["HTTP_USER_AGENT"]
    if agent == "" and "User-Agent" in environ:
        agent = environ["User-Agent"]
    return agent


def is_ip_in_subnet(ip, subnet):
    try:
        ip_obj = ipaddress.ip_address(ip)
        if "/" in subnet:
            subnet_obj = ipaddress.ip_network(subnet, strict=False)
        else:
            # 如果子网是一个单独的 IP 地址，则将其视为 /32 的子网
            subnet_obj = ipaddress.ip_network(f"{subnet}/32", strict=False)
        return ip_obj in subnet_obj
    except ValueError:
        return False


def is_ip_in_whitelist(ip, whitelist):
    try:
        ip_obj = ipaddress.ip_address(ip)
    except ValueError:
        return False

    for item in whitelist.split(","):
        try:
            if "/" in item:
                subnet_obj = ipaddress.ip_network(item)
                if ip_obj in subnet_obj:
                    return True
            else:
                if ip_obj == ipaddress.ip_address(item):
                    return True
        except ValueError:
            pass

    return False

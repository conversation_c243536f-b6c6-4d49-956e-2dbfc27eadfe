import base64
from http.cookies import SimpleCookie
import time 
import logging
from saml2 import time_util

 
def refresh_idpauthn_cookie(environ, resp,timeout):
        # 从self.environ中获取idpauthn cookie
        cookies = environ.get("HTTP_COOKIE", "")
        cookie = SimpleCookie(cookies)

        if "idpauthn" in cookie:
            if timeout and timeout>0:
                cookie["idpauthn"]["expires"] = _expiration( 60 * timeout)
            cookie["idpauthn"]["path"] = "/"
            cookie["idpauthn"]["samesite"] = "Lax"
            cookie["idpauthn"]["httponly"] = True
            cookie["idpauthn"]["secure"] = True
            kaka = tuple(cookie.output().split(": ", 1))
            resp.headers.append(kaka)
def set_cookie(name,timeout, _, *args):
    cookie = SimpleCookie()

    data = ":".join(args)
    if not isinstance(data, bytes):
        data = data.encode("ascii")

    data64 = base64.b64encode(data)
    if not isinstance(data64, str):
        data64 = data64.decode("ascii")

    cookie[name] = data64
    cookie[name]["path"] = "/"
    cookie[name]["samesite"] = "Lax"
    cookie[name]["httponly"] = True
    cookie[name]["secure"] = True
    if timeout and timeout>0:
        cookie[name]["expires"] = _expiration(((60 * timeout)))  # 5 minutes from now    
        
         
    return tuple(cookie.output().split(": ", 1))
def _expiration(timeout, tformat="%a, %d-%b-%Y %H:%M:%S GMT"):
    """

    :param timeout:
    :param tformat:
    :return:
    """
    if timeout == "now":
        return time_util.instant(tformat)
    elif timeout == "dawn":
        return time.strftime(tformat, time.gmtime(0))
    else:
        # validity time should match lifetime of assertions
        return time_util.in_a_while(seconds=timeout, format=tformat)


def info_from_cookie(kaka,sessionmanager): 
    if kaka:
        cookie_obj = SimpleCookie(kaka)
        morsel = cookie_obj.get("idpauthn", None)
        if morsel:
            try:
                data = base64.b64decode(morsel.value)
                if not isinstance(data, str):
                    data = data.decode("ascii")
                key, ref = data.split(":", 1)
                userinfo=sessionmanager.get_session(key)
                if userinfo:
                    return userinfo, ref
            except (KeyError, TypeError):
                return None,None
 #       else:
#            logger.debug("No idpauthn cookie")
    return None,None


def delete_cookie(environ, name):
    kaka = environ.get("HTTP_COOKIE", "") 
    if kaka:
        cookie_obj = SimpleCookie(kaka)
        morsel = cookie_obj.get(name, None)
        cookie = SimpleCookie()
        cookie[name] = ""
        cookie[name]["path"] = "/"
        cookie[name]["samesite"] = "Lax"
        cookie[name]["httponly"] = True
        cookie[name]["secure"] = True
        cookie[name]["expires"] = _expiration("dawn")
        return tuple(cookie.output().split(": ", 1))
    return None


def refresh_cookie_expiry(cookie_str,minutes):
    cookie = SimpleCookie()
    cookie.load(cookie_str)

    # 获取cookie名称
    cookie_name = list(cookie.keys())[0]

    # 计算新的过期时间
    new_expiry = _expiration(60 *minutes)

    # 更新cookie的过期时间
    cookie[cookie_name]["expires"] = new_expiry
    cookie[cookie_name]["samesite"] = "Lax"
    cookie[cookie_name]["httponly"] = True
    cookie[cookie_name]["secure"] = True
    return tuple(cookie.output().split(": ", 1))
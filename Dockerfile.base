# Step 1: Create a base image with system dependencies
FROM python:3.10-slim as base

# Set timezone to Shanghai
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone
# Create /etc/apt/sources.list and add Tsinghua mirrors
RUN echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main" > /etc/apt/sources.list && \
    echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian-security/ bookworm-security main" >> /etc/apt/sources.list

# Install system dependencies
RUN apt update && apt install -y     build-essential     python3-dev     xmlsec1     libcairo2-dev 
RUN apt-get clean &&  rm -rf /var/lib/apt/lists/*

# Step 2: Create the build stage
FROM base as builder

# Set the working directory in the container
WORKDIR /app

# Configure pip to use Tsinghua's mirror
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# Upgrade pip, setuptools, and wheel to the latest version
RUN pip install --upgrade pip setuptools wheel

# Install Cython via pip
RUN pip install cython

# Copy only requirements.txt first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code into the container
COPY . .

# Run ldconfig to update shared library cache
RUN ldconfig

# Step 3: Create the runtime image
FROM python:3.10-slim as runtime

# Set timezone to Shanghai
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 使用清华大学的Debian镜像源
# Create /etc/apt/sources.list and add Tsinghua mirrors
RUN echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main" > /etc/apt/sources.list && \
    echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian-security/ bookworm-security main" >> /etc/apt/sources.list


# Install runtime dependencies
RUN apt update && apt install -y     build-essential     python3-dev     xmlsec1     libcairo2-dev   build-essential curl apt-utils gnupg2 
RUN apt-get clean &&  rm -rf /var/lib/apt/lists/*

COPY  microsoft.asc /etc/apt/trusted.gpg.d/microsoft.asc

COPY  mssql-release.list /etc/apt/sources.list.d/mssql-release.list
RUN apt update
RUN env ACCEPT_EULA=Y apt-get install -y msodbcsql18
#COPY /odbc.ini / 
#RUN odbcinst -i -s -f /odbc.ini -l 
# openssl_conf = openssl_init

# [openssl_init]
# ssl_conf = ssl_sect

 

 
# Step 2: Append to /usr/lib/ssl/openssl.cnf
RUN echo " openssl_conf = openssl_init" >> /usr/lib/ssl/openssl.cnf && \
    echo "[openssl_init]" >> /usr/lib/ssl/openssl.cnf && \
    echo "ssl_conf = ssl_sect" >> /usr/lib/ssl/openssl.cnf && \
    echo "[ssl_sect]" >> /usr/lib/ssl/openssl.cnf && \
    echo "system_default = system_default_sect" >> /usr/lib/ssl/openssl.cnf && \
    echo "" >> /usr/lib/ssl/openssl.cnf && \
    echo "[system_default_sect]" >> /usr/lib/ssl/openssl.cnf && \
    echo "CipherString = DEFAULT:@SECLEVEL=0" >> /usr/lib/ssl/openssl.cnf && \
    echo "MinProtocol = TLSv1.0" >> /usr/lib/ssl/openssl.cnf && \
    echo "Options = UnsafeLegacyServerConnect" >> /usr/lib/ssl/openssl.cnf
RUN apt install -y  unixodbc-dev
# Set the working directory in the container
WORKDIR /app

# Copy the application code and installed dependencies from the build stage
COPY --from=builder /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
COPY --from=builder /app /app

# Run ldconfig to update shared library cache
RUN ldconfig 
# Define the command to run the application
CMD ["python", "idp.py", "idp_conf"]

#!/usr/bin/env python
import json
import cherrypy
import argparse
import base64
from hashlib import sha1
from http.cookies import SimpleCookie
import cherrypy
from middleware.environ_middleware import EnvironPassingMiddleware

import importlib
import os
import re
import time
from urllib.parse import parse_qs
from cache.redis_cache import RedisCache

from config import MyConfig
from idp_user import EXTRA
from mako.lookup import TemplateLookup

from request_response_logger import RequestResponseLoggerTool 
from rsahelper.decrypt_password import decrypt_password_from_default 
from saml2 import BINDING_HTTP_ARTIFACT
from saml2 import BINDING_HTTP_POST
from saml2 import BINDING_HTTP_REDIRECT
from saml2 import BINDING_PAOS
from saml2 import BINDING_SOAP
from saml2 import BINDING_URI
from saml2 import server
from saml2 import time_util
from saml2.authn import is_equal
from saml2.authn_context import PASSWORD
from saml2.authn_context import UNSPECIFIED
from saml2.authn_context import AuthnBroker
from saml2.authn_context import authn_context_class_ref
from saml2.httputil import BadRequest, LoginFail, LoginTimeOut, LogoutResponse, getpath
from saml2.httputil import NotFound
from saml2.httputil import Redirect
from saml2.httputil import Response
from saml2.httputil import ServiceError
from saml2.httputil import Unauthorized
from saml2.httputil import get_post
from saml2.httputil import geturl
from saml2.ident import Unknown
from saml2.metadata import create_metadata_string
from saml2.profile import ecp
from saml2.s_utils import PolicyError
from saml2.s_utils import UnknownPrincipal
from saml2.s_utils import UnsupportedBinding
from saml2.s_utils import exception_trace
from saml2.s_utils import rndstr
from saml2.sigver import encrypt_cert_from_item
from saml2.sigver import verify_redirect_signature
import saml2.xmldsig as ds
from saml_message.saml_message import SAMLMessage
from saml_message.saml_storage import SAMLStorage
from services.ldapauthenticator import ActiveDirectory
from services.user import loginWithCode
from session.session_mananger import SessionManager
from utility import cookie
from cache.cache_util import CacheUtil
from utility.ip import allow_ip, get_real_loc, get_remote_ip, get_user_agent
from utility.password_validator import PasswordValidator
from webapi.work_wechat.work_wechat import work_wechat
from api.load_api.api_loader import ApiLoader

try:
    from cheroot.ssl.builtin import BuiltinSSLAdapter
    from cheroot.wsgi import Server as WSGIServer
except ImportError:
    from cherrypy.wsgiserver import CherryPyWSGIServer as WSGIServer
    from cherrypy.wsgiserver.ssl_builtin import BuiltinSSLAdapter
from logger.logger import basic_log, log, log_error, log_info, msg_log

logger = log()


# -----------------------------------------------------------------------------


def dict2list_of_tuples(d):
    return [(k, v) for k, v in d.items()]


# -----------------------------------------------------------------------------


class Service:
    def __init__(self, environ, start_response, user=None):
        self.environ = environ
        self.start_response = start_response
        self.user = user

    def unpack_redirect(self):
        if "QUERY_STRING" in self.environ:
            _qs = self.environ["QUERY_STRING"]
            return {k: v[0] for k, v in parse_qs(_qs).items()}
        else:
            return None

    def unpack_post(self):
        post_data = get_post(self.environ)
        _dict = parse_qs(
            post_data if isinstance(post_data, str) else post_data.decode("utf-8")
        )
 
        try:
            return {k: v[0] for k, v in _dict.items()}
        except Exception as e:
            logger.error(exception_trace(e))
            return None

    def unpack_soap(self):
        try:
            query = get_post(self.environ)
            return {"SAMLRequest": query, "RelayState": ""}
        except Exception:
            return None

    def unpack_either(self):
        if self.environ["REQUEST_METHOD"] == "GET":
            _dict = self.unpack_redirect()
        elif self.environ["REQUEST_METHOD"] == "POST":
            _dict = self.unpack_post()
        else:
            _dict = None 
        return _dict

    def operation(self, saml_msg, binding):
        if not (saml_msg and "SAMLRequest" in saml_msg):
            resp = BadRequest("Error parsing request or no request")
            return resp(self.environ, self.start_response)
        else:
            # saml_msg may also contain Signature and SigAlg
            if "Signature" in saml_msg:
                try:
                    kwargs = {
                        "signature": saml_msg["Signature"],
                        "sigalg": saml_msg["SigAlg"],
                    }
                except KeyError:
                    resp = BadRequest("Signature Algorithm specification is missing")
                    return resp(self.environ, self.start_response)
            else:
                kwargs = {}

            try:
                kwargs["encrypt_cert"] = encrypt_cert_from_item(
                    saml_msg["req_info"].message
                )
            except KeyError:
                pass

            try:
                kwargs["relay_state"] = saml_msg["RelayState"]
            except KeyError:
                pass

            resp = self.do(saml_msg["SAMLRequest"], binding, **kwargs)
            return resp

    def artifact_operation(self, saml_msg):
        if not saml_msg:
            resp = BadRequest("Missing query")
            return resp(self.environ, self.start_response)
        else:
            # exchange artifact for request
            request = IDP.artifact2message(saml_msg["SAMLart"], "spsso")
            try:
                return self.do(request, BINDING_HTTP_ARTIFACT, saml_msg["RelayState"])
            except KeyError:
                return self.do(request, BINDING_HTTP_ARTIFACT)

    def refresh_cookie(self):
        # 获取idpauthn cookie
        idpauthn_cookie = cookie.get_idpauthn_cookie()

        # 刷新cookie有效期
        if idpauthn_cookie:
            # 假设有效期为一小时
            expires = time.time() + 60 * 60 * 72
            idpauthn_cookie["expires"] = expires

    def response(self, binding, http_args):
        resp = None
        if binding == BINDING_HTTP_ARTIFACT:
            resp = Redirect()
        elif http_args["data"]:
            resp = Response(http_args["data"], headers=http_args["headers"])
        else:
            for header in http_args["headers"]:
                if header[0] == "Location":
                    resp = Redirect(header[1])

        if not resp:
            resp = ServiceError("Don't know how to return response")
        # 刷新cookie有效期
        #  cookie.info_from_cookie()
        #  cookie.refresh_idpauthn_cookie(self.environ, resp)
        # kaka = set_cookie("idpauthn", "/", uid, query["authn_reference"][0])
        return resp(self.environ, self.start_response)

    def do(self, query, binding, relay_state="", encrypt_cert=None):
        pass

    def redirect(self):
        """Expects a HTTP-redirect request"""

        _dict = self.unpack_redirect()
        return self.operation(_dict, BINDING_HTTP_REDIRECT)

    def post(self):
        """Expects a HTTP-POST request"""

        _dict = self.unpack_post()
        return self.operation(_dict, BINDING_HTTP_POST)

    def artifact(self):
        # Can be either by HTTP_Redirect or HTTP_POST
        _dict = self.unpack_either()
        return self.artifact_operation(_dict)

    def soap(self):
        """
        Single log out using HTTP_SOAP binding
        """
        _dict = self.unpack_soap()
        return self.operation(_dict, BINDING_SOAP)

    def uri(self):
        _dict = self.unpack_either()
        return self.operation(_dict, BINDING_SOAP)

    def not_authn(self, key, requested_authn_context):
        ruri = geturl(self.environ, query=False)

        kwargs = dict(authn_context=requested_authn_context, key=key, redirect_uri=ruri)
        # Clear cookie, if it already exists
        kaka = cookie.delete_cookie(self.environ, "idpauthn")
        if kaka:
            kwargs["headers"] = [kaka]
        return do_authentication(self.environ, self.start_response, **kwargs)


# -----------------------------------------------------------------------------


REPOZE_ID_EQUIVALENT = "uid"
FORM_SPEC = """<form name="myform" method="post" action="%s">
   <input type="hidden" name="SAMLResponse" value="%s" />
   <input type="hidden" name="RelayState" value="%s" />
</form>"""


# -----------------------------------------------------------------------------
# === Single log in ====
# -----------------------------------------------------------------------------


class AuthenticationNeeded(Exception):
    def __init__(self, authn_context=None, *args, **kwargs):
        Exception.__init__(*args, **kwargs)
        self.authn_context = authn_context


class SSO(Service):
    def __init__(self, environ, start_response, user=None):

        Service.__init__(self, environ, start_response, user)

        self.binding = ""
        self.response_bindings = None
        self.resp_args = {}
        self.binding_out = None
        self.destination = None
        self.req_info = None
        self.op_type = ""
        self.samlStorage = SAMLStorage(IDP.cache)

    def verify_request(self, query, binding):
        """
        :param query: The SAML query, transport encoded
        :param binding: Which binding the query came in over
        """
        resp_args = {}
        if not query:            
            resp = Unauthorized("Unknown user")
            return resp_args, resp(self.environ, self.start_response)

        if not self.req_info:
            self.req_info = IDP.parse_authn_request(query, binding)
            
        _authn_req = self.req_info.message

        try:
            self.binding_out, self.destination = IDP.pick_binding(
                "assertion_consumer_service",
                bindings=self.response_bindings,
                entity_id=_authn_req.issuer.text,
                request=_authn_req,
            )
        except Exception as err:
            logger.error("Couldn't find receiver endpoint: %s", err)
            raise

        logger.debug("Binding: %s, destination: %s", self.binding_out, self.destination)

        resp_args = {}
        try:
            resp_args = IDP.response_args(_authn_req)
            _resp = None
        except UnknownPrincipal as excp:
            _resp = IDP.create_error_response(_authn_req.id, self.destination, excp)
        except UnsupportedBinding as excp:
            _resp = IDP.create_error_response(_authn_req.id, self.destination, excp)

        return resp_args, _resp

    def do(self, query, binding_in, relay_state="", encrypt_cert=None, **kwargs):
        """

        :param query: The request
        :param binding_in: Which binding was used when receiving the query
        :param relay_state: The relay state provided by the SP
        :param encrypt_cert: Cert to use for encryption
        :return: A response
        """
        try:
            resp_args, _resp = self.verify_request(query, binding_in)
        except UnknownPrincipal as excp:
            resp = ServiceError(f"UnknownPrincipal: {excp}")
            return resp(self.environ, self.start_response)
        except UnsupportedBinding as excp:
            logger.error("UnsupportedBinding: %s", excp)
            resp = ServiceError(f"UnsupportedBinding: {excp}")
            return resp(self.environ, self.start_response)

        if not _resp:
            identity = {
                "upn": self.user,
            }


            if REPOZE_ID_EQUIVALENT:
                identity[REPOZE_ID_EQUIVALENT] = self.user
            try:
                try:
                    metod = self.environ["idp.authn"]
                except KeyError:
                    pass
                else:
                    resp_args["authn"] = metod

                _resp = IDP.create_authn_response(
                    identity,
                    userid=self.user,
                    encrypt_cert_assertion=encrypt_cert,
                    **resp_args,
                )
            except Exception as excp:
                logger.error(exception_trace(excp))
                resp = ServiceError(f"Exception: {excp}")
                return resp(self.environ, self.start_response)

       # logger.info("AuthNResponse: end", _resp)
        if self.op_type == "ecp":
            kwargs = {
                "soap_headers": [
                    ecp.Response(assertion_consumer_service_url=self.destination)
                ]
            }
        else:
            kwargs = {}

        http_args = IDP.apply_binding(
            self.binding_out,
            f"{_resp}",
            self.destination,
            relay_state,
            response=True,
            **kwargs,
        )

        resp = self.response(self.binding_out, http_args)

        return resp

    @staticmethod
    def _store_request(saml_msg):
        key = sha1(saml_msg["SAMLRequest"].encode()).hexdigest()
        # store the AuthnRequest
        #  IDP.ticket[key] = saml_msg
        samlStroage = SAMLStorage(IDP.cache)
        saml_message = SAMLMessage.from_dict(saml_msg)
        samlStroage.store_saml_message(key, saml_message)
        return key

    def redirect(self):
        """This is the HTTP-redirect endpoint"""

        saml_msg = self.unpack_redirect()

        try:
            if "key" in saml_msg:
                _key = saml_msg["key"]
                if "SAMLRequest" not in saml_msg:
                    samlStroage = SAMLStorage(IDP.cache)
                    saml_msg = samlStroage.get_saml_message(_key)
                    samlStroage.del_saml_message(_key)
                else:
                    log_info(self.environ,"Redirect",{"msg":"登录过期，未查找到有效SAML" + _key})               
                    resp = LoginTimeOut("登录过期")
                    return resp(self.environ, self.start_response)
            if saml_msg is None or "SAMLRequest" not in saml_msg:
                logger.info(log)
                log_info(self.environ,"Redirect",{"msg":"登录过期，未查找到有效SAML" + _key})             
                resp = LoginTimeOut("因您长时间未扫码，登录无效")
                return resp(self.environ, self.start_response)
            sigalg = None
            signature = None
            relay_state = None
            if "SigAlg" in saml_msg:
                sigalg = saml_msg["SigAlg"]
            if "Signature" in saml_msg:
                signature = saml_msg["Signature"]
            if "RelayState" in saml_msg:
                relay_state = saml_msg["RelayState"]
            self.req_info = IDP.parse_authn_request(
                saml_msg["SAMLRequest"],
                BINDING_HTTP_REDIRECT,
                signature=signature,
                sigalg=sigalg,
                relay_state=relay_state,
            )
            if not self.req_info:
                resp = LoginFail("消息转换无效")
                return resp(self.environ, self.start_response)

            _req = self.req_info.message

            if "SigAlg" in saml_msg and "Signature" in saml_msg:
                # Signed request
                issuer = _req.issuer.text
                _certs = IDP.metadata.certs(issuer, "any", "signing")
                verified_ok = False
                for cert_name, cert in _certs:
                    if verify_redirect_signature(saml_msg, IDP.sec.sec_backend, cert):
                        verified_ok = True
                        break
                if not verified_ok:
                    resp = LoginFail("签名验证无效，请重新登录")
                    log_info(self.environ,"Redirect",{"msg":"签名验证无效，请重新登录" + _key})     
                    return resp(self.environ, self.start_response)

            if self.user:
                saml_msg["req_info"] = self.req_info
                if _req.force_authn is not None and _req.force_authn.lower() == "true":
                    key = self._store_request(saml_msg)
                    return self.not_authn(key, _req.requested_authn_context)
                else:
                    return self.operation(saml_msg, BINDING_HTTP_REDIRECT)
            else:
                saml_msg["req_info"] = self.req_info
                key = self._store_request(saml_msg)
                return self.not_authn(key, _req.requested_authn_context)
        except KeyError:
            resp = LoginFail("签名验证无效，请重新登录")
            log_info(self.environ,"Redirect",{"msg":"签名验证无效，请重新登录" + _key})     
            return resp(self.environ, self.start_response)

    def post(self):
        """
        The HTTP-Post endpoint
        """        
        saml_msg = self.unpack_either()

        try:
            _key = saml_msg["key"]
            saml_msg = IDP.ticket[_key]
            self.req_info = saml_msg["req_info"]
            del IDP.ticket[_key]
        except KeyError:
            self.req_info = IDP.parse_authn_request(
                saml_msg["SAMLRequest"], BINDING_HTTP_POST
            )
            _req = self.req_info.message
            if self.user:
                if _req.force_authn is not None and _req.force_authn.lower() == "true":
                    saml_msg["req_info"] = self.req_info
                    key = self._store_request(saml_msg)
                    return self.not_authn(key, _req.requested_authn_context)
                else:
                    return self.operation(saml_msg, BINDING_HTTP_POST)
            else:
                saml_msg["req_info"] = self.req_info
                key = self._store_request(saml_msg)
                return self.not_authn(key, _req.requested_authn_context)
        else:
            return self.operation(saml_msg, BINDING_HTTP_POST)

    # def artifact(self):
    # # Can be either by HTTP_Redirect or HTTP_POST
    #     _req = self._store_request(self.unpack_either())
    #     if isinstance(_req, basestring):
    #         return self.not_authn(_req)
    #     return self.artifact_operation(_req)

    def ecp(self):
        # The ECP interface
        resp = None

        try:
            authz_info = self.environ["HTTP_AUTHORIZATION"]
            if authz_info.startswith("Basic "):
                try:
                    _info = base64.b64decode(authz_info[6:])
                except TypeError:
                    resp = Unauthorized()
                else:
                    try:
                        (user, passwd) = _info.split(":")
                        #    if is_equal(PASSWD[user], passwd): //20240602 基础
                        resp = Unauthorized()
                        self.user = user
                        self.environ["idp.authn"] = AUTHN_BROKER.get_authn_by_accr(
                            PASSWORD
                        )
                    except ValueError:
                        resp = Unauthorized()
            else:
                resp = Unauthorized()
        except KeyError:
            resp = Unauthorized()

        if resp:
            return resp(self.environ, self.start_response)

        _dict = self.unpack_soap()
        self.response_bindings = [BINDING_PAOS]
        # Basic auth ?!
        self.op_type = "ecp"
        return self.operation(_dict, BINDING_SOAP)


# -----------------------------------------------------------------------------
# === Authentication ====
# -----------------------------------------------------------------------------


def do_authentication(
    environ, start_response, authn_context, key, redirect_uri, headers=None
):
    """
    Display the login form
    """
    auth_info = AUTHN_BROKER.pick(authn_context)

    if len(auth_info):
        method, reference = auth_info[0]
        return method(environ, start_response, reference, key, redirect_uri, headers)
    else:
        resp = Unauthorized("No usable authentication method")
        return resp(environ, start_response)


# -----------------------------------------------------------------------------


def username_password_authn(
    environ, start_response, reference, key, redirect_uri, headers=None
):
    """
    Display the login form
    """

    login_template = MyConfig().get('Login', 'template', 'login.mako')
    kwargs = dict(mako_template=login_template, template_lookup=LOOKUP)
    if headers:
        kwargs["headers"] = headers

    resp = Response(**kwargs)
    my_config = MyConfig().config
    appid = my_config["WeWork"]["app_id"]
    agentid = my_config["WeWork"]["agent_id"]
    remember_text = MyConfig().get("SessionManager", "remember_text", "长期有效")
    
    isWhite = allow_ip(environ)
    if not isWhite:
        remember_text = ""

    agentid = my_config["WeWork"]["agent_id"]

    argv = {
        "action": "/sso/verify",
        "login": "",
        "password": "",
        "validateCode": "",
        "key": key,
        "agentid": agentid,
        "appid": appid,
        "remember": "0",
        "remember_text": remember_text,
        "authn_reference": reference,
        "redirect_uri": redirect_uri,
    }
    return resp(environ, start_response, **argv)


def remove_shecc_domain(original_string):
    domain = MyConfig().get("Auth", "domain", "shecc.com")
    parts = original_string.split("@")
    if len(parts) > 1 and parts[1].endswith(domain):
        return "@".join(parts[:1])
    return original_string


def add_prefix_if_not_present(original_string, prefix=None):
    if prefix is None:
        prefix = MyConfig().get("Auth", "prefix", "shecc\\")
    original_string = remove_shecc_domain(original_string)
    if original_string.startswith(prefix):
        return original_string  # 如果已存在，则不添加前缀
    else:
        return f"{prefix}{original_string}"  # 如果不存在，则添加前缀


def verify_username_and_password(dic):
    # verify username and password
    username = dic["login"][0]
    source_password = dic["password"][0]
    password=decrypt_password_from_default(source_password)
    code = dic["validateCode"][0]
    remeber = ""
    username = add_prefix_if_not_present(username)
    rv, email = loginWithCode(username, password, code)
    return rv, email


def do_verify(environ, start_response, _):
    query_str = get_post(environ)
    if not isinstance(query_str, str):
        query_str = query_str.decode("ascii")
    query = parse_qs(query_str)
    resp = None
    #   logger.debug("do_verify: %s", query)
    ip = get_remote_ip(environ)
    # environ['HTTP_X_REAL_IP']
    agent = get_user_agent(environ)  # ['HTTP_USER_AGENT']
    loc = get_real_loc(environ)
    log_obj = basic_log(environ, "Login")
    username = query["login"][0]
    userkey = query["key"][0]
    environ["pre_user"] =username
    rv = {}
    user = {}
    try:
        rv, user = verify_username_and_password(query)
        _ok = rv["code"] == 0
        log_obj["user_name"] = username
        log_obj["login_status"] = _ok
        log_obj["login_type"] = "sms"
        log_obj["msg"] = rv
        if rv["code"] == 1:
            cacheUtil = CacheUtil()
            cacheUtil.set_repwd(userkey, environ["HTTP_REFERER"], username)
            lox = f"/sso/reset?key={userkey}"    
            logger.info({"msg":"用户首次登录需要更改密码","login_status":_ok,"login_type":"sms","msg":rv,"user_name":username}) 
            log_info(environ, "Login",{"msg":"用户首次登录需要更改密码","login_status":_ok,"login_type":"sms","msg":rv,"user_name":username})
           
            resp = Redirect(lox, content="text/html")
            return resp(environ, start_response)
    except KeyError as e: 
        log_error(environ,"Login",{"error":e,"login_status":False,"login_type":"Login","msg":rv,"user_name":username})
        # logger.info(f"用户登录失败{ip},{query_str}，错误信息 {e}")
        _ok = False
        user = None
    except Exception as e:
        log_error(environ,"Login",{"error":e,"login_status":False,"login_type":"Login","msg":rv,"user_name":username})
        _ok = False
    if not _ok:
        log_error(environ,"Login",{"error":"用户登录失败","login_status":False,"login_type":"Login","msg":rv,"user_name":username})
        resp = LoginFail("登录失败，请确认信息后重新登录")
    else:
        uid = rndstr(24)
        remeber = ""
        allow = allow_ip(env=environ)
        IDP.session_manager.add_session(
            uid, user, ip, remeber, "sms", agent, loc, allow
        )
        timeout = IDP.session_manager.getTimeout(remeber)
        kaka = cookie.set_cookie(
            "idpauthn", timeout, "/", uid, query["authn_reference"][0]
        )
        
        # 20250613 添加ip_web 登录成功记录
        from cache.user_cache import user_login_success
        user_login_success(username,ip,loc) 
        log_info(environ,"Login",{"session":uid,"remeber":remeber,"user_name":username,"expire":timeout})
        lox = f"{query['redirect_uri'][0]}?id={uid}&key={query['key'][0]}"
        resp = Redirect(lox, headers=[kaka], content="text/html")
    
    return resp(environ, start_response)


def not_found(environ, start_response):
    """Called if no URL matches."""
    resp = NotFound()
    return resp(environ, start_response)


# -----------------------------------------------------------------------------
# === Single log out ===
# -----------------------------------------------------------------------------


# def _subject_sp_info(req_info):
#    # look for the subject
#    subject = req_info.subject_id()
#    subject = subject.text.strip()
#    sp_entity_id = req_info.message.issuer.text.strip()
#    return subject, sp_entity_id


class ResetPassword(Service):

    def __init__(self, environ, start_response, user=None):
        Service.__init__(self, environ, start_response, user)

    def check_post_params(self, params):
        # Check if the username is provided and not empty
        if "user_name" not in params or params["user_name"].strip() == "":
            return False, "用户名称不能为空"

        # Check if the current password is provided and not empty
        if "current_password" not in params or params["current_password"].strip() == "":
            return False, "请输入当前密码"

        # Check if the new password is provided and not empty
        if "new_password" not in params or params["new_password"].strip() == "":
            return False, "请输入新密码"
        if "key" not in params or params["key"].strip() == "":
            return False, "您无法执行此操作"
        new_password = params["new_password"]

        # Check if the new password length is at least 8 characters
        if len(new_password) < 8:
            return False, "密码不能小于8位"
        
        
        
        # Check if the new password contains at least three of the following:
        # lowercase letters, uppercase letters, digits, special characters
    
        return True, ""

    def reset(self):
        """
        The HTTP-Post endpoint
        """
        # 从 environ 中获取请求方法
        params = self.unpack_either()
 
        result, msg = self.check_post_params(params)

        
        log = basic_log(self.environ, "resetpwd")
        log["status"]=False
        rv = {"code": 0, "message": ""}
        if result:
            oldpwd = decrypt_password_from_default( params["current_password"])
            newpwd = decrypt_password_from_default(params["new_password"])
            
            password_validator= PasswordValidator(params["user_name"])            
            result,msg=password_validator.validate(newpwd)
            if(not result):
                rv["code"]=4016
                rv["message"]=msg
            else:
            
                key = params["key"]            
                log["key"]=key            
                cache = CacheUtil()
                user_obj = cache.get_repwd(key)

                if user_obj is None:
                    rv["code"] = 4006
                    rv["message"] = "已过期，请重新访问业务系统"

                elif params["user_name"] != user_obj["user_name"]:
                    rv["code"] = 4005
                    rv["message"] = "非法操作"

                else:
                    user_name = user_obj["user_name"]

                    if oldpwd==newpwd:
                        rv["code"] = 4007
                        rv["message"] = "新旧密码不能相同"
                    result, message = ActiveDirectory().user_reset_password(
                        user_name, oldpwd, newpwd
                    )
                    log["user_name"]=user_name
                    
                    rv["code"] = 0 if result else 4010
                    rv["message"] = message

                    if rv["code"] == 0:
                        rv["redirect"] = user_obj["url"]
                        cache.del_repwd(key=key)
                        
                        log["status"]=True

        else:
            rv["code"] = 4000
            rv["message"] = msg

        
        log["message"] = rv
        
        logger.info(json.dumps( log))
        
        resp = Response(json.dumps(rv).encode(), content="application/json")
        # resp = self.json_response(rv, self.start_response)
        return resp(self.environ, self.start_response)
    

    def json_response(self, data, start_response):
        start_response("200 OK", [("Content-Type", "application/json")])
        return json.dumps(data).encode()
    

    def redirect(self):
        reset_template = MyConfig().get('Login', 'resetTemplate', 'reset_password.mako')
        kwargs = dict(mako_template=reset_template, template_lookup=LOOKUP)
        resp = Response(**kwargs)
        params = self.unpack_either()
        argv = {}
        log = basic_log(self.environ, "resetpwd")
        
        if "key" not in params:
            log["msg"] = "重置密码错误，大概率非法操作！"
            resp = LoginTimeOut("未找到您的登录信息")
        else:
            key = params["key"]
            cache = CacheUtil()
            user_obj = cache.get_repwd(key)

            if user_obj == None:
                log["msg"] = "未查找到用户信息"
                resp = LoginTimeOut("未找到您的登录信息")
            else:
                argv = {"user_name": user_obj["user_name"], "key": key}

        logger.info(json.dumps( log))
        return resp(self.environ, self.start_response, **argv)


class Logout(Service):
    def redirect(self):
 
        kaka = self.environ.get("HTTP_COOKIE", None)
        
        if kaka:            
            user = cookie.info_from_cookie(kaka, IDP.session_manager)
            if user[0] and "session_id" in user[0]:
                IDP.session_manager.clear_session(user[0]["session_id"])
        else:            
            no_cookie_log=basic_log(self.environ,"signout")
            no_cookie_log["msg"]="使用signout,不存在cookie"
            no_cookie_log["msg_type"]="no_cookie"
            logger.log(json.dumps(no_cookie_log))

        delco = cookie.delete_cookie(self.environ, "idpauthn")
        resp = LogoutResponse("您已经成功退出登录", headers=[delco])
        return resp(self.environ, self.start_response)


class SLO(Service):
    def do(self, request, binding, relay_state="", encrypt_cert=None, **kwargs):

        try:
            req_info = IDP.parse_logout_request(request, binding)
        except Exception as exc:
            logger.error("Bad request: %s", exc)
            resp = BadRequest(f"{exc}")
            return resp(self.environ, self.start_response)

        msg = req_info.message
        if msg.name_id:

            # remove the authentication
            try:
                lid = IDP.ident.find_local_id(msg.name_id)
                # IDP.session_manager.clean_expired_sessions(lid)
                IDP.session_db.remove_authn_statements(msg.name_id)
            except Exception as exc:
                logger.error("Unknown session: %s", exc)
                # resp = ServiceError("Unknown session: %s", exc)
                # return resp(self.environ, self.start_response)
                # pass

        resp = IDP.create_logout_response(msg, [binding])

        if binding == BINDING_SOAP:
            destination = ""
            response = False
        else:
            binding, destination = IDP.pick_binding(
                "single_logout_service", [binding], "spsso", req_info
            )
            response = True

        try:
            hinfo = IDP.apply_binding(
                binding, f"{resp}", destination, relay_state, response=response
            )
        except Exception as exc:
            logger.error("ServiceError: %s", exc)
            resp = ServiceError(f"{exc}")
            return resp(self.environ, self.start_response)

        # _tlh = dict2list_of_tuples(hinfo["headers"])
        delco = cookie.delete_cookie(self.environ, "idpauthn")
        if delco:
            hinfo["headers"].append(delco)

        if binding == BINDING_HTTP_REDIRECT:
            for key, value in hinfo["headers"]:
                if key.lower() == "location":
                    resp = Redirect(value, headers=hinfo["headers"])
                    return resp(self.environ, self.start_response)

            resp = ServiceError("missing Location header")
            return resp(self.environ, self.start_response)
        else:
            resp = Response(hinfo["data"], headers=hinfo["headers"])
            return resp(self.environ, self.start_response)


# ----------------------------------------------------------------------------
# Manage Name ID service
# ----------------------------------------------------------------------------


class NMI(Service):
    def do(self, query, binding, relay_state="", encrypt_cert=None):
        req = IDP.parse_manage_name_id_request(query, binding)
        request = req.message

        # Do the necessary stuff
        name_id = IDP.ident.handle_manage_name_id_request(
            request.name_id, request.new_id, request.new_encrypted_id, request.terminate
        )


        _resp = IDP.create_manage_name_id_response(request)

        # It's using SOAP binding
        hinfo = IDP.apply_binding(
            BINDING_SOAP, f"{_resp}", "", relay_state, response=True
        )

        resp = Response(hinfo["data"], headers=hinfo["headers"])
        return resp(self.environ, self.start_response)


# ----------------------------------------------------------------------------
# === Assertion ID request ===
# ----------------------------------------------------------------------------


# Only URI binding
class AIDR(Service):
    def do(self, aid, binding, relay_state="", encrypt_cert=None):

        try:
            assertion = IDP.create_assertion_id_request_response(aid)
        except Unknown:
            resp = NotFound(aid)
            return resp(self.environ, self.start_response)

        hinfo = IDP.apply_binding(BINDING_URI, f"{assertion}", response=True)
        resp = Response(hinfo["data"], headers=hinfo["headers"])
        return resp(self.environ, self.start_response)

    def operation(self, _dict, binding, **kwargs):
        if not _dict or "ID" not in _dict:
            resp = BadRequest("Error parsing request or no request")
            return resp(self.environ, self.start_response)

        return self.do(_dict["ID"], binding, **kwargs)


# ----------------------------------------------------------------------------
# === Artifact resolve service ===
# ----------------------------------------------------------------------------


class ARS(Service):
    def do(self, request, binding, relay_state="", encrypt_cert=None):
        _req = IDP.parse_artifact_resolve(request, binding)

        msg = IDP.create_artifact_response(_req, _req.artifact.text)

        hinfo = IDP.apply_binding(BINDING_SOAP, f"{msg}", "", "", response=True)

        resp = Response(hinfo["data"], headers=hinfo["headers"])
        return resp(self.environ, self.start_response)


# ----------------------------------------------------------------------------
# === Authn query service ===
# ----------------------------------------------------------------------------


# Only SOAP binding
class AQS(Service):
    def do(self, request, binding, relay_state="", encrypt_cert=None):
        _req = IDP.parse_authn_query(request, binding)
        _query = _req.message

        msg = IDP.create_authn_query_response(
            _query.subject, _query.requested_authn_context, _query.session_index
        )

        hinfo = IDP.apply_binding(BINDING_SOAP, f"{msg}", "", "", response=True)

        resp = Response(hinfo["data"], headers=hinfo["headers"])
        return resp(self.environ, self.start_response)


# ----------------------------------------------------------------------------
# === Attribute query service ===
# ----------------------------------------------------------------------------


# Only SOAP binding
class ATTR(Service):
    def do(self, request, binding, relay_state="", encrypt_cert=None):

        _req = IDP.parse_attribute_query(request, binding)
        _query = _req.message

        name_id = _query.subject.name_id
        uid = name_id.text
        identity = EXTRA[uid]

        # Comes in over SOAP so only need to construct the response
        args = IDP.response_args(_query, [BINDING_SOAP])
        msg = IDP.create_attribute_response(identity, name_id=name_id, **args)

        hinfo = IDP.apply_binding(BINDING_SOAP, f"{msg}", "", "", response=True)

        resp = Response(hinfo["data"], headers=hinfo["headers"])
        return resp(self.environ, self.start_response)


# ----------------------------------------------------------------------------
# Name ID Mapping service
# When an entity that shares an identifier for a principal with an identity
# provider wishes to obtain a name identifier for the same principal in a
# particular format or federation namespace, it can send a request to
# the identity provider using this protocol.
# ----------------------------------------------------------------------------


class NIM(Service):
    def do(self, query, binding, relay_state="", encrypt_cert=None):
        req = IDP.parse_name_id_mapping_request(query, binding)
        request = req.message
        # Do the necessary stuff
        try:
            name_id = IDP.ident.handle_name_id_mapping_request(
                request.name_id, request.name_id_policy
            )
        except Unknown:
            resp = BadRequest("Unknown entity")
            return resp(self.environ, self.start_response)
        except PolicyError:
            resp = BadRequest("Unknown entity")
            return resp(self.environ, self.start_response)

        info = IDP.response_args(request)
        _resp = IDP.create_name_id_mapping_response(name_id, **info)

        # Only SOAP
        hinfo = IDP.apply_binding(BINDING_SOAP, f"{_resp}", "", "", response=True)

        resp = Response(hinfo["data"], headers=hinfo["headers"])
        return resp(self.environ, self.start_response)


# ----------------------------------------------------------------------------
# Cookie handling
# ----------------------------------------------------------------------------


# ----------------------------------------------------------------------------


# map urls to functions
AUTHN_URLS = [
    # sso
    # (r"sso/post$", (SSO, "post")),
    # (r"sso/post/(.*)$", (SSO, "post")),
    (r"sso/redirect$", (SSO, "redirect")),
    (r"sso/redirect/(.*)$", (SSO, "redirect")),
    # (r"sso/art$", (SSO, "artifact")),
    # (r"sso/art/(.*)$", (SSO, "artifact")),
    # slo
    (r"slo/redirect$", (SLO, "redirect")),
    (r"slo/redirect/(.*)$", (SLO, "redirect")),
    (r"slo/signout", (Logout, "redirect")),
    # (r"slo/post$", (SLO, "post")),
    # (r"slo/post/(.*)$", (SLO, "post")),
    # (r"slo/soap$", (SLO, "soap")),
    # (r"slo/soap/(.*)$", (SLO, "soap")),
    # 20240607 注释
    # (r"airs$", (AIDR, "uri")),
    # (r"ars$", (ARS, "soap")),
    # # mni
    # (r"mni/post$", (NMI, "post")),
    # (r"mni/post/(.*)$", (NMI, "post")),
    # (r"mni/redirect$", (NMI, "redirect")),
    # (r"mni/redirect/(.*)$", (NMI, "redirect")),
    # (r"mni/art$", (NMI, "artifact")),
    # (r"mni/art/(.*)$", (NMI, "artifact")),
    # (r"mni/soap$", (NMI, "soap")),
    # (r"mni/soap/(.*)$", (NMI, "soap")),
    # # nim
    # (r"nim$", (NIM, "soap")),
    # (r"nim/(.*)$", (NIM, "soap")),
    #
    # (r"aqs$", (AQS, "soap")),
    # (r"attr$", (ATTR, "soap")),
]

NON_AUTHN_URLS = [
    # (r'login?(.*)$', do_authentication),
    (r"sso/verify?(.*)$", do_verify),
    (r"sso/reset$", (ResetPassword, "redirect")),
    (r"sso/reset_pwd$", (ResetPassword, "reset")),
    #  (r"sso/ecp$", (SSO, "ecp")),
]


# ----------------------------------------------------------------------------


def metadata(environ, start_response):
    try:
        path = args.path[:]
        if path is None or len(path) == 0:
            path = os.path.dirname(os.path.abspath(__file__))
        if path[-1] != "/":
            path += "/"
        metadata = create_metadata_string(
            path + args.config,
            IDP.config,
            args.valid,
            args.cert,
            args.keyfile,
            args.id,
            args.name,
            args.sign,
        )
        start_response("200 OK", [("Content-Type", "text/xml")])
        return [metadata]
    except Exception as ex:
        logger.error("An error occured while creating metadata: %s", ex.message)
        return not_found(environ, start_response)


def staticfile(environ, start_response):
    try:
        path = ""
        if path is None or len(path) == 0:
            path = os.path.dirname(os.path.abspath(__file__))
        if path[-1] != "/":
            path += "/"
        path += environ.get("PATH_INFO", "").lstrip("/")
        path = os.path.realpath(path)
        start_response("200 OK", [("Content-Type", "text/xml")])
        return open(path).read()
    except Exception as ex:
        logger.error("An error occured while creating metadata: %s", ex.message)
        return not_found(environ, start_response)


def application(environ, start_response):
    """
    The main WSGI application. Dispatch the current request to
    the functions from above and store the regular expression
    captures in the WSGI environment as  `myapp.url_args` so that
    the functions from above can access the url placeholders.

    If nothing matches, call the `not_found` function.

    :param environ: The HTTP application environment
    :param start_response: The application to run when the handling of the
        request is done
    :return: The response as a list of lines
    """
    
    path = environ.get("PATH_INFO", "").lstrip("/")
    # if re.search(r"static/.*", path) is not None:  20240226 静态文件
    #     return staticfile(environ, start_response)
    if path == "idp.xml":
        return metadata(environ, start_response)
    
    kaka = environ.get("HTTP_COOKIE", None)
    environ["trace_id"] = rndstr(16)
    
    user = ""
    if kaka:
        userinfo, authn_ref = cookie.info_from_cookie(kaka, IDP.session_manager)
        if authn_ref:
            environ["idp.authn"] = AUTHN_BROKER[authn_ref]
        if userinfo:
            IDP.session_manager.refresh_session(userinfo["session_id"])
            user = userinfo["username"]
            environ["user"]=userinfo
        # 更新响应头中的 Set-Cookie
        # 更新新的 Set-Cookie

    else:
        try:
            query = parse_qs(environ["QUERY_STRING"])
            userinfo = IDP.session_manager.get_session(query["id"][0])
            user = None
            if userinfo:
                IDP.session_manager.refresh_session(query["id"][0])
                user = userinfo["username"]
        except KeyError:
            user = None
    
    url_patterns = AUTHN_URLS
    if not user:
        url_patterns = NON_AUTHN_URLS + url_patterns
    try:
        if re.search(r"sso/api/work/.*", path) is not None:
            return wechat_instance.handle_api_request(
                path, environ, start_response, IDP
            )
        for regex, callback in url_patterns:
            match = re.search(regex, path)

            if match is not None:
                try:                     
                    environ["myapp.url_args"] = match.groups()[0]
                except IndexError:
                    environ["myapp.url_args"] = path
                calllog=basic_log(environ, callback)
                calllog["msg"]=""
                
                try:
                    if isinstance(callback, tuple):                        
                        
                        cls = callback[0](environ, start_response, user)
                        func = getattr(cls, callback[1])
                        resp_func_result= func()
                        return resp_func_result
                    
                    callback_result= callback(environ, start_response, user)
                    return callback_result
                
                except Exception as e:
                    loginfo = basic_log(environ, "callback")
                    loginfo["msg"] = exception_trace(e)
                    logger.error(loginfo)
                    res = LoginTimeOut("日志ID：" + environ["trace_id"] + ",未知异常，请联系管理员")    
                    return res(environ, start_response)

    except Exception as e:
        loginfo = basic_log(environ, "Sys")
        loginfo["msg"] = exception_trace(e)
        logger.error(loginfo)
        res = LoginTimeOut("日志ID：" + environ["trace_id"] + ",未知异常，请联系管理员")    
        return res(environ, start_response)
 
    log_info(environ,"404",{"msg":"404 NOT Found"})
 
    res = NotFound("404 NOT FOUND")    
    return res(environ, start_response)


# ----------------------------------------------------------------------------
class Root(object):
    def __init__(self, wsgi_app):
        self.wsgi_app = wsgi_app

    @cherrypy.expose
    def default(self, *args, **kwargs):
        return self.wsgi_app(   
            environ=cherrypy.request.wsgi_environ,
            start_response=cherrypy.response.wsgi_start_response,
        )


class APIHandler(object):
    @cherrypy.expose
    def index(self):
        return "API Root"

    @cherrypy.expose
    def operation(self, id=None):
        return f"Operation with id: {id}"

    def custom_error_handler(status, message, traceback, version):
        logger.error(f"Status: {status}, Message: {message}, Traceback: {traceback}")
        return {"code": 999999, "message": "出现未知错误，请稍后重试"}


# 自定义404错误页面处理函数
def error_page_404(status, message, traceback, version):
    # 记录404错误信息到日志
    log = basic_log(cherrypy.request.headers, "404")
    log["message"] = {"res_code": 404, "msg": message}
    logger.error(log)
    # 返回一个通用的404错误消息给前端
    return "404 Not Found"


# 自定义500错误页面处理函数
def error_page_500(status, message, traceback, version):
    # 记录500错误信息到日志
    log = basic_log(cherrypy.request.headers, "500")
    log["message"] = {"res_code": 500, "msg": message, "tracerback": traceback}
    logger.error(log)
    # 返回一个通用的404错误消息给前端
    # 返回一个通用的500错误消息给前端
    return "500 ERROR"


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-p", dest="path", help="Path to configuration file.", default="configs/idp_conf.py"
    )
    parser.add_argument(
        "-v",
        dest="valid",
        help="How long, in days, the metadata is valid from " "the time of creation",
    )
    parser.add_argument("-c", dest="cert", help="certificate")
    parser.add_argument("-i", dest="id", help="The ID of the entities descriptor")
    parser.add_argument(
        "-k", dest="keyfile", help="A file with a key to sign the metadata with"
    )
    parser.add_argument("-n", dest="name")
    parser.add_argument(
        "-s", dest="sign", action="store_true", help="sign the metadata"
    )
    parser.add_argument("-m", dest="mako_root", default="./")
    parser.add_argument(dest="config")
    args = parser.parse_args()

    CONFIG = importlib.import_module(args.config)

    AUTHN_BROKER = AuthnBroker()
    AUTHN_BROKER.add(
        authn_context_class_ref(PASSWORD), username_password_authn, 10, CONFIG.BASE
    )
    AUTHN_BROKER.add(authn_context_class_ref(UNSPECIFIED), "", 0, CONFIG.BASE)

    IDP = server.Server(args.config, cache=RedisCache())
    # 配置静态文件服务

    # 挂载您的WSGI应用
    IDP.ticket = {}

    IDP.session_manager = SessionManager()
    _rot = args.mako_root
    LOOKUP = TemplateLookup(
        directories=[f"{_rot}templates",f"{_rot}htdocs/trusit", f"{_rot}htdocs"],
        module_directory=f"{_rot}modules",
        input_encoding="utf-8",
        output_encoding="utf-8",
    )

    HOST = CONFIG.HOST
    PORT = CONFIG.PORT

    sign_alg = None
    digest_alg = None
    try:
        sign_alg = CONFIG.SIGN_ALG
    except AttributeError:
        pass
    try:
        digest_alg = CONFIG.DIGEST_ALG
    except AttributeError:
        pass
    ds.DefaultSignature(sign_alg, digest_alg)
    wechat_instance = work_wechat()
    # SRV = WSGIServer((HOST, PORT), application)
    # 配置CherryPy
    # 静态文件目录配置
    # 静态文件目录配置
    static_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static")
    # 挂载您的WSGI应用到根路径

    api_directory = os.path.join(os.path.dirname(__file__), "api")
    # 应用全局配置

    # 使用 ApiLoader 加载和挂载 API
    ApiLoader.load_and_mount_api_scripts(api_directory, "/ssoapi")
    # 应用中间件包装应用程序
    wrapped_app = EnvironPassingMiddleware(application)
    cherrypy.tree.graft(wrapped_app, "/")
    # 单独配置静态文件服务
    cherrypy.tree.mount(
        None,
        "/sso/static",
        config={"/": {"tools.staticdir.on": True, "tools.staticdir.dir": static_dir}},
    )
     
    # 如果没有LogAccess属性则默认为False
    cherryaccessflag = getattr(CONFIG, 'LogAccess', False)
    cherrypy.config.update(
        {
            "tools.my_tool.on": True,
            "global": {
                "log.screen": True,  # 关闭默认错误消息显示
                "error_page.404": error_page_404,
                "error_page.500": error_page_500,
                "server.socket_host": "0.0.0.0",
                "server.socket_port": 80,
                "engine.autoreload.on": True,  # 根据需要开启或关闭自动重载
                "error_page.default": APIHandler.custom_error_handler,  # 设置默认错误页面处理器'
                'tools.proxy.on': True,
            # 如果你的代理需要处理特定的 base URL 路径，可能需要配置 base
            # 'tools.proxy.base': 'http://your.domain.com/app_prefix',

                # 禁用默认的 log_access Tool
               # 'tools.log_access.on': cherryaccessflag,
                # 启用我们自定义的 log Tool
                # 确保这个 Tool 在 proxy Tool 之后执行（before_finalize 阶段通常在 proxy 之后）
               #'tools.custom_access_logger.on': True,
               # 'tools.request_response_logger.on': cherryaccessflag,  # 启用全局请求和响应日志记录工具
              #     'tools.error_logger.on': True  # 启用全局错误日志记录工具
                # 其他全局配置...
            },
            # 错误页面配置
            "/": {
                "error_page.404": error_page_404,
                "error_page.500": error_page_500,
            },
            # 正确配置中间件
            'tools.wsgi_middleware.on': True,
            'tools.wsgi_middleware.middleware': [EnvironPassingMiddleware],
        }
    )
    # context = ssl.SSLContext(ssl.PROTOCOL_TLSv1_2)

    # cherrypy.server.ssl_context = context   # 启动服务器
    # _https = ""
    # if CONFIG.HTTPS:
    #     https = "using HTTPS"
    #     cherrypy.server.ssl_module = "builtin"
    #     cherrypy.server.ssl_certificate = CONFIG.SERVER_CERT
    #     cherrypy.server.ssl_private_key = CONFIG.SERVER_KEY
    #     cherrypy.server.ssl_certificate_chain = CONFIG.CERT_CHAIN

    # if CONFIG.HTTPS:
    #     https = "using HTTPS"
    #     # SRV.ssl_adapter = ssl_pyopenssl.pyOpenSSLAdapter(
    #     #     config.SERVER_CERT, config.SERVER_KEY, config.CERT_CHAIN)
    #     SRV.ssl_adapter = BuiltinSSLAdapter(CONFIG.SERVER_CERT, CONFIG.SERVER_KEY, CONFIG.CERT_CHAIN)
 
    logger.info("Server starting")
    # CherryPy应用中
 #print(f"IDP listening on  {PORT}{_https}")
    # try:
    cherrypy.log.access_log.propagate = False
    cherrypy.log.access_log.handlers = []
  #  cherrypy.tools.request_response_logger = RequestResponseLoggerTool()
    cherrypy.engine.start()
    cherrypy.engine.block()
    # SRV.start()
    # except KeyboardInterrupt:
    # SRV.stop()

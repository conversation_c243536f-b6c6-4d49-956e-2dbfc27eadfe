(function(window) {
  
    var encodedPublicKey = "TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF3STN2bm4vTWRWVFdjaHpLa2txbVpTeU5waWdoWHhlaTJHOHVrKzR6aGd4M0hKUDdvWnNndXpKQmtaMlZWVEROL3NYR3A4QXhybnNmMS9jdlcyYm44bmVqdUYyaThhck42UXpLbmtYcTJVcTUycmhCWEhNVHBCVVZzMEQ3dndtam0xTFBGM1BuUitFSWNjTllYR0h2L1liUGx0TFFWd2pjSnI1WWxhZG0reTJNUFI5VGYraU1aR3ZCRVR3eGRkTnZxUG5tRlptUy95TGJxd3N5Q1JJTEJuTk5OMXhBRkZyUlhRSFZvSUJaTkk5NXU5elVtTm9wYy9xcUFlak9wSTNjb3V5S1E2a3VYSjFvSkFDOTN1dmsyQzUyS3BVYjNTY043WHNzOWsyalBtNWtmT0hxRTdYQXU2bHQwczZ3dGdLNGlGRHpJM0JsTUI0Vmo5d01KWDBBRXdJREFRQUI=";

    // 解码公钥
    function decodePublicKey(encodedKey) {
        return atob(encodedKey);
    }

    // 加密函数
    function encryptPassword(plaintext) {
        var JSEncrypt = window.JSEncrypt;
        var publicKey = decodePublicKey(encodedPublicKey);
        var encrypt = new JSEncrypt();
        encrypt.setPublicKey(publicKey);
        return encrypt.encrypt(plaintext);
    }

    // 将加密函数暴露给全局对象
    window.encryptPassword = encryptPassword;
})(window);

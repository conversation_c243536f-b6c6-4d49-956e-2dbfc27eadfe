(function(window) {
  
    var encodedPublicKey = "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEZ3JEUS91ajZDNFp2ck1mMUdoZUx1Uk5oZgpiM2tubG1jVXZiYmVsS3FWSXZuK2cyNGV4bzd2U2libzdGYzhtaUhKQVd6bTBudTN2eHpyR3NwQ0E2a3BVQXhrCnVBNHBud1NnMFg1SURhNWdBL2taM0w0dnZrMk5RWXY1OUoxditDb0hIVHUxeCtRTk5yejUrMWNjMkUyeE1iL2cKeUpaWVI4alB5azFDWEdQaVRRSURBUUFCCi0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQ==";

    // 解码公钥
    function decodePublicKey(encodedKey) {
        return atob(encodedKey);
    }

    // 加密函数
    function encryptPassword(plaintext) {
        var JSEncrypt = window.JSEncrypt;
        var publicKey = decodePublicKey(encodedPublicKey);
        var encrypt = new JSEncrypt();
        encrypt.setPublicKey(publicKey);
        return encrypt.encrypt(plaintext);
    }

    // 将加密函数暴露给全局对象
    window.encryptPassword = encryptPassword;
})(window);

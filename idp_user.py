# from dirg_util.dict import LDAPDict
# ldap_settings = {
#    "ldapuri": "ldaps://ldap.test.umu.se",
#    "base": "dc=umu, dc=se",
#    "filter_pattern": "(uid=%s)",
#    "user": "",
#    "passwd": "",
#    "attr": [
#        "eduPersonScopedAffiliation",
#        "eduPersonAffiliation",
#        "eduPersonPrincipalName",
#        "givenName",
#        "sn",
#        "mail",
#        "uid",
#        "o",
#        "c",
#        "labeledURI",
#        "ou",
#        "displayName",
#        "norEduPersonLIN"
#    ],
#    "keymap": {
#        "mail": "email",
#        "labeledURI": "labeledURL",
#    },
#    "static_values": {
#        "eduPersonTargetedID": "one!for!all",
#    },
#    "exact_match": True,
#    "firstonly_len1": True,
#    "timeout": 15,
# }
# Uncomment to use a LDAP directory instead.
# USERS = LDAPDict(**ldap_settings)

USERS = {
    "lvjw": {
        "upn":"<EMAIL>",       
    },
    "roland": {
        "sn": "Hedberg",
        "givenName": "Roland",
        "eduPersonScopedAffiliation": "<EMAIL>",
        "eduPersonPrincipalName": "<EMAIL>",
        "uid": "rohe",
        "eduPersonTargetedID": ["one!for!all"],
        "c": "SE",
        "o": "Example Co.",
        "ou": "IT",
        "initials": "P",
        # "schacHomeOrganization": "example.com",
        "mail": "<EMAIL>",
        "displayName": "P. Roland Hedberg",
        "labeledURL": "http://www.example.com/rohe My homepage",
        "norEduPersonNIN": "SE197001012222",
    },
    "babs": {"surname": "Babs", "givenName": "Ozzie", "eduPersonAffiliation": "affiliate"},
    "upper": {"surname": "Jeter", "givenName": "Derek", "eduPersonAffiliation": "affiliate"},
}

EXTRA = {
    "roland": {
        "eduPersonEntitlement": "urn:mace:swamid.se:foo:bar",
        "schacGender": "male",
        "schacUserPresenceID": "skype:pepe.perez",
    }
}

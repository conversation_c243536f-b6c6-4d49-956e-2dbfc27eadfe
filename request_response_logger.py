import traceback
import cherrypy
import logging
from logger.logger import log

# 获取配置好的logger
logger = log()

class RequestResponseLoggerTool(cherrypy.Tool):
    def __init__(self):
        cherrypy.Tool.__init__(self, 'before_handler', self.log_request_response)

    def log_request_response(self):
        request = cherrypy.request
        response = cherrypy.response
        logger.debug("Call stack: %s", "".join(traceback.format_stack()))
    # 其他检查逻辑
       # logger.info(f"{request.method} {request.path_info} - {response.status}")

 

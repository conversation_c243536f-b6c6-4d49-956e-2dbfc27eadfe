import ipaddress
from datetime import datetime, timedelta
import json
import threading
import time
import redis
import schedule
from config import MyConfig


from cache.redis_cache import RedisCache
from logger.logger import log


class SessionManager:
    def __init__(self):
        self.redis_client = RedisCache()
        self.safe_last_run_success = False
        self.safe_last_run_time = None
        self.uid2userinfo_keyname = "session:uid2session"
        self.username2uid_keyname = "session:session2uids"

        self.logger = log()
        # 设置定时清理任务
        config = MyConfig()
        self.expiry_time = int(
            config.get("SessionManager", "session_cleartime", 300)
        )  # 过期时间（秒）
        self.session_timeout = int(config.get("SessionManager", "session_timeout", 20))
        self.ip_whitelist = config.get(
            "SessionManager", "ip_whitelist", "10.0.0.0/24,***********"
        )
        self.remember_timeout = int(
            config.get("SessionManager", "remember_timeout", 20)
        )
        self.remember_max_count = int(
            config.get("SessionManager", "remember_max_count", 10)
        )
        self.session_cleartime = int(
            config.get("SessionManager", "session_cleartime", 5)
        )

        # 初始化时运行一次清理任务
        # self.clean_expired_sessions()
        schedule.every(self.session_cleartime).minutes.do(self.clean_expired_sessions)
        self.run_schedule()

    def add_session(
        self, session_id, user_name, ip, remember, logingtype, useragent, loc, internal
    ):
        sessionInfo = {
            "username": user_name,
            "ip": ip,
            "session_id": session_id,
            "remember": remember,
            "logintype": logingtype,
            "useragent": useragent,
            "loc": loc,
            "internal": internal,
        }
        self.add_session_userinfo(session_id, sessionInfo)
        return sessionInfo

    def getTimeout(self, remember):
        expiration_time = (
            self.remember_timeout
            if remember == "" or remember == 0
            else self.session_timeout
        )
        return expiration_time

    def add_session_userinfo(self, session_id, userinfo):
        now = datetime.now()
        userinfo["time"] = now.timestamp()
        userinfo["last_time"] = now.timestamp()
        expiration_time = (
            self.session_timeout
            if userinfo["remember"] == "" or userinfo["remember"] == "0"
            else self.remember_timeout
        )
        expiration_time_seconds = expiration_time * 60
        expiration_timestamp = now + timedelta(seconds=expiration_time_seconds)
        userinfo["expiration_timestamp"] = expiration_timestamp.timestamp()
        session_ids = []
        if self.redis_client.hexists(self.username2uid_keyname, userinfo["username"]):
            session_ids = self.redis_client.hget_obj(
                self.username2uid_keyname, userinfo["username"]
            )
        session_ids.append(session_id)
        self.redis_client.hset_obj(self.uid2userinfo_keyname, session_id, userinfo)
        self.redis_client.hset_obj(
            self.username2uid_keyname, userinfo["username"], session_ids
        )

    def refresh_session(self, session_id):
        userinfo = self.redis_client.hget_obj(self.uid2userinfo_keyname, session_id)
        now = datetime.now()
        userinfo["last_time"] = now.timestamp()
        expiration_time = (
            self.session_timeout
            if userinfo["remember"] == "" or userinfo["remember"] == "0"
            else 0
        )
        if expiration_time > 0:
            expiration_time_seconds = expiration_time * 60
            expiration_timestamp = now + timedelta(seconds=expiration_time_seconds)
            userinfo["expiration_timestamp"] = expiration_timestamp.timestamp()
        self.redis_client.hset_obj(self.uid2userinfo_keyname, session_id, userinfo)

    def clear_session(self, session_id):
        userinfo = self.redis_client.hget_obj(self.uid2userinfo_keyname, session_id)
        self.logger.info(f"清理{session_id}")
        sessionIds = self.redis_client.hget_obj(
            self.username2uid_keyname, userinfo["username"]
        )
        filtered_session_ids = [
            session_Id for session_Id in sessionIds if session_Id != session_id
        ]
        self.redis_client.hset_obj(
            self.username2uid_keyname, userinfo["username"], filtered_session_ids
        )
        self.redis_client.hdel(self.uid2userinfo_keyname, session_id)

    def get_session(self, session_id):
        if not self.redis_client.hexists(self.uid2userinfo_keyname, session_id):
            return None
        session_data = self.redis_client.hget_obj(self.uid2userinfo_keyname, session_id)
        expiration_time = datetime.fromtimestamp(session_data["expiration_timestamp"])
        if datetime.now() > expiration_time:
            return None
        return session_data

    def get_sessions_by_username(self, username):
        session_ids = self.redis_client.hget_obj(self.uid2userinfo_keyname, username)
        user_sessions = []
        for session_id in session_ids:
            session_data = self.get_session(session_id)
            user_sessions.append(session_data)
        return user_sessions

    def run_schedule(self):
        # 创建一个新线程来运行调度任务
        schedule_thread = threading.Thread(target=self.run_schedule_thread)
        schedule_thread.start()

    def run_schedule_thread(self):
        # 主循环
        while True:
            schedule.run_pending()
            time.sleep(1)  # 适当的休眠时间，减少 CPU 占用

    def clean_expired_sessions(self):
        try:
            self.logger.info("开始清理Session")
            session_ids = self.redis_client.hkeys(self.uid2userinfo_keyname)
            for session_id in session_ids:
                try:
                    userinfo = self.redis_client.hget_obj(
                        self.uid2userinfo_keyname, session_id
                    )

                    expiration_timestamp = datetime.fromtimestamp(
                        float(userinfo["expiration_timestamp"])
                    )
                    if datetime.now() > expiration_timestamp+ timedelta(days=10):
                        self.logger.info(
                            "清理%s的Session：%s,过期时间：%s,创建时间：%s",
                            userinfo["username"],
                            session_id,
                            expiration_timestamp,
                            userinfo["time"],
                        )
                        sessionIds = self.redis_client.hget_obj(
                            self.username2uid_keyname, userinfo["username"]
                        )
                        filtered_session_ids = [
                            session_Id
                            for session_Id in sessionIds
                            if session_Id != session_id
                        ]
                        self.redis_client.hset_obj(
                            self.username2uid_keyname,
                            userinfo["username"],
                            filtered_session_ids,
                        )
                        self.redis_client.hdel(self.uid2userinfo_keyname, session_id)
                except Exception as e:
                    self.logger.info(f"Session清理失败{e},{session_id}")
                    pass
            self.safe_last_run_success = True
            self.logger.info("Session清理结束")
        except Exception as e:
            self.logger.info(f"Session清理失败{e}")
            print(f"An error occurred in cleanup task: {e}")
            self.safe_last_run_success = False

    # finally:
    #  self.safe_last_run_time = time.time()

    def is_safe_last_run_success(self):
        return self.safe_last_run_success

    def get_safe_last_run_time(self):
        return self.safe_last_run_time

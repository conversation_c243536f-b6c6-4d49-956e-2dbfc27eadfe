import logging
from data.EHRModels.SysEmployee import SysEmployee
from data.dal.database import SQLServerSession
from services.verification import send_verification_code,verify_verification_code
from data.models.SMSVerification import SMSVerification, VerificationStatus, VerificationType
from services.ldapauthenticator import ActiveDirectory
from logger.logger import log
from config import MyConfig
from datetime import timedelta
from datetime import datetime
logger=log()
def send_validate_sms(username):
    # 去除两端空格后判断是否为空
    username = username.strip()
    if not username:
        return {"code": 1000, "message": "用户名不能为空",}
    user= ActiveDirectory().get_user_phone(username)
    # 查询用户是否存在
   # session = SQLServerSession()
   # user = session.query(SysEmployee).filter_by(ItCode=username).first()
    
    if not user:
     #   logger.info("未查找到用户信息：%s", username)
        return {"code": 1001, "message": "登录异常,未查找到用户信息，请检查您的登录信息或联系系统管理员"}
    if "userAccountControl" in user:
        if user.userAccountControl.value & 0x0002:  # 0x0002 表示账户已禁用
            logger.info(f"用户 {username} 已被禁用。")
            return {"code": 1001, "message":f"用户 {username} 已被禁用。"}
    # 判断账号状态
    if 'lockoutTime' in user:
            lockout_time = user['lockoutTime'].value
            if str(lockout_time) != '1601-01-01 00:00:00+00:00':
                # 从配置读取解锁等待时间
                unlock_minutes = int(MyConfig().get('Auth', 'minutes', '3'))
                # 计算解锁时间
                unlock_time = lockout_time + timedelta(minutes=unlock_minutes)
                # 判断当前时间是否超过解锁时间
                # 将datetime.now()转换为带时区的时间以匹配lockout_time
                current_time = datetime.now(lockout_time.tzinfo)
                if current_time < unlock_time:
                    logger.info("用户被锁定，用户信息：%s", username)
                    return {"code": 1002, "message": "登录异常，您的账号已被锁定"}
    mobile=user["mobile"].value
    # 发送验证码短信
    result = send_verification_code(mobile,VerificationType.LOGIN)

    # 返回发送结果
    return {"code": result["code"], "message": result["message"]}


def loginWithCode(username, password, validateCode):
    if not username or not password or not validateCode:

        return {"code": 1000, "message": "用户名、密码和验证码不能为空"},None
    # 去除两端空格后判断是否为空
    username = username.strip()
    password = password.strip()
    validateCode = validateCode.strip()
       # 查询用户是否存在
    user= ActiveDirectory().get_user_phone(username)
    # 查询用户是否存在
   # session = SQLServerSession()
   # user = session.query(SysEmployee).filter_by(ItCode=username).first()
    
    if not user:
        return {"code": 1001, "message": "登录异常，请检查您的登录信息或联系系统管理员"}
    if "userAccountControl" in user:
        if user.userAccountControl.value & 0x0002:  # 0x0002 表示账户已禁用
            logger.info(f"用户 {username} 已被禁用。")
            return {"code": 1001, "message":f"用户 {username} 已被禁用。"}
    # 判断账号状态
    if 'lockoutTime' in user:
        lockout_time = user['lockoutTime'].value
        if str(lockout_time) != '1601-01-01 00:00:00+00:00':
            # 从配置读取解锁等待时间
            unlock_minutes = int(MyConfig().get('Auth', 'minutes', '3'))
            # 计算解锁时间
            unlock_time = lockout_time + timedelta(minutes=unlock_minutes)
            # 判断当前时间是否超过解锁时间
            # 将datetime.now()转换为带时区的时间以匹配lockout_time
            current_time = datetime.now(lockout_time.tzinfo)
            if current_time < unlock_time:
                logger.info("用户被锁定，用户信息：%s", username)
                return {"code": 1002, "message": "登录异常，您的账号已被锁定"}
    mobile = user["mobile"].value
     
   


    # 验证验证码
    verification_result = verify_verification_code(mobile, validateCode)
    if verification_result["code"] != 0:
        return verification_result,None
    email=user["mail"].value
    if email and "trusit.net" in email:
        email=email.split("@")[0]+"@corp.trusit.com"
    # 验证 LDAP 密码
    ldap_auth_result,username,error = ActiveDirectory().authenticate_user(username, password)
    logger.info("%s LDAP 验证结果：%s,错误消息:%s",username, ldap_auth_result,error)
    if ldap_auth_result:

            
        return {"code": 0, "message": "登录成功"},email
    elif error=="773":
        return {"code":1,"message":"用户登录前需要更改密码"},user["mail"].value
    else:
        return {"code": 1002, "message": "用户名或密码不正确"},None

from datetime import datetime, timedelta
from data.models.SMSVerification import SMSVerification, VerificationStatus, VerificationType
from aliyun.aliyun_sms import AliyunSMS
import random
import string
from data.dal.mysqlmanager import get_session
from logger.logger import log
logger=log()
def generate_verification_code():
    """生成六位随机验证码"""
    return ''.join(random.choices(string.digits, k=6))

def send_sms_verification_code(phone_number, verification_code):
    """发送短信验证码的逻辑，实际情况根据需要替换为真实的发送短信操作"""
    logger.info(f"向 {phone_number} 发送验证码：{verification_code}")
    return AliyunSMS().send_sms(phone_number, {"code": verification_code})

def get_last_verification(session, phone_number, verification_type):
    """获取指定手机号和验证码类型的最近一条验证码记录"""
    return session.query(SMSVerification).filter_by(
        phone_number=phone_number, verification_type=verification_type
    ).order_by(SMSVerification.timestamp.desc()).first()

def update_verification_and_send_code(session, last_verification, new_verification_code):
    """更新验证码信息并发送新验证码"""
    last_verification.verification_code = new_verification_code
    last_verification.request_count += 1
    last_verification.timestamp = datetime.now()
    send_result = send_sms_verification_code(last_verification.phone_number, new_verification_code)
    logger.info(f"{last_verification.phone_number}验证码发送结果{send_result}")
    return send_verification_result(send_result)

def send_verification_result(send_result): 
    if send_result["Code"] == "OK":
        print("验证码发送成功。")
        return {"code": 0, "message": "验证码发送成功"}
    else:
        print("验证码发送失败。")
        return {"code": 500, "message": f"验证码发送失败, {send_result['Message']}"}

def send_verification_code(phone_number, verification_type):
    """发送验证码的主要逻辑"""
    with get_session() as session:
        last_verification = get_last_verification(session, phone_number, verification_type)

        if not last_verification or last_verification.verification_status == VerificationStatus.USED:
            # 情况1：验证码已使用或未找到，生成新验证码并发送
            new_verification_code = generate_verification_code()
            save_verification_code_to_database(session, phone_number, new_verification_code, verification_type)
            send_result = send_sms_verification_code(phone_number, new_verification_code)
            return send_verification_result(send_result)

        elif last_verification.timestamp < datetime.now() - timedelta(hours=1):
            # 情况2：验证码未使用但已过期，生成新验证码并发送
            new_verification_code = generate_verification_code()
            save_verification_code_to_database(session, phone_number, new_verification_code, verification_type)
            send_result = send_sms_verification_code(phone_number, new_verification_code)
            return send_verification_result(send_result)

        elif last_verification.timestamp >= datetime.now() - timedelta(hours=1) and last_verification.request_count < 3:
            # 情况3：验证码未使用，在1小时内，更新并发送
            return update_verification_and_send_code(session, last_verification, last_verification.verification_code)

        else:
            # 情况4：其他条件不满足，无法发送验证码
            return {"code": 400, "message": "无法发送验证码。已达到发送限制或上一个验证码未使用。"}

def save_verification_code_to_database(session, phone_number, verification_code, verification_type):
    """保存验证码到数据库"""
    verification = SMSVerification(
        phone_number=phone_number, 
        verification_code=verification_code, 
        verification_type=verification_type
    )
    session.add(verification)

def verify_verification_code(phone_number, input_code):
    """验证验证码是否正确"""
    with get_session() as session:
        last_verification = session.query(SMSVerification).filter_by(
            phone_number=phone_number, verification_type=VerificationType.LOGIN
        ).order_by(SMSVerification.timestamp.desc()).first()

        if last_verification and last_verification.verification_status == VerificationStatus.UNUSED and last_verification.timestamp >= datetime.now() - timedelta(hours=1):
            if last_verification.verification_code == input_code:
                last_verification.verification_status = VerificationStatus.USED
                session.add(last_verification)
                return {"code": 0, "message": "验证码验证通过"}
            else:
                return {"code": 400, "message": "验证码不正确"}
        else:
            return {"code": 400, "message": "验证码无效"}

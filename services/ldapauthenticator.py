import ssl
from ldap3 import MODIFY_REPLACE, Server, Connection, ALL, SUBTREE, Tls
import ldap3
from config import MyConfig
class ActiveDirectory:
    def __init__(self):
        my_config=MyConfig().config
        
        self.serverurl = my_config['Ldap']['url']
        self.user = my_config['Ldap']['user']
        self.password = my_config['Ldap']['password']
        self.searchbase = my_config['Ldap']['base']
        self.connection = None

    def connect(self):
        try:
            self.server = Server(self.serverurl, get_info=ALL, use_ssl=True, tls=Tls(validate=ssl.CERT_NONE))
            self.connection = Connection(self.server, user=self.user, password=self.password, auto_bind=True)
            return True
        except Exception as e:
            print(f'Error connecting to LDAP: {e}')
            return False
    def user_reset_password(self,username,oldpwd,newpwd):
        result={}
        if not self.connection:
            if not self.connect():
               return False,"无法连接上游ad，连接失败"
        try:
            
            user_dn=self.get_user_dn(username)
            if user_dn is None:
                result={"code":2000,"message":"未找到用户信息"}
                return False,"未找到用户信息"
            conn=Connection(self.server, user=user_dn, password=oldpwd)
            if not conn.bind():            
                if conn.result['description'] == 'invalidCredentials':                    
                    error_message = conn.result['message']
                    
                if 'data 773' in error_message:  # 773 表示用户必须在下次登录时更改密码
                    if not self.connection:
                        if not self.connect():
                            return False,"无法连接上游ad，连接失败"
                    rv =ldap3.extend.microsoft.modifyPassword.ad_modify_password(self.connection, user_dn, newpwd, oldpwd,  controls=None)
                    if rv:                        
                        return rv,"修改密码成功"   
                    return rv,"请确认设置的密码是否符合密码复杂度规则"
                return False,"密码输入有误，请确认后重新输入"
            #todo 支持修改密码功能
            # else:
            #     rv =ldap3.extend.microsoft.modifyPassword.ad_modify_password(self.connection, user_dn, newpwd, oldpwd,  controls=None)
            #     if rv:                        
            #         return rv,"修改密码成功"  
            #     return rv,"请确认设置的密码是否符合密码复杂度规则"        
        except ldap3.core.exceptions.LDAPCursorAttributeError as ldapEx:
            print(ldapEx)
        except Exception as e:    
            return False,f"修改密码出现错误,{e}"       
            
        return False,"登录失败，ad 验证不通过"
    def get_user_dn(self,username):
        if not self.connection:
            if not self.connect():
                return False
        parts = username.split('\\')
            # 取最后一个元素
        username = parts[-1]
        username = parts[-1]
        user_filter = f'(&(objectCategory=person)(objectClass=user)(sAMAccountName={username}))'
        attributes = ['*', '+']
        self.connection.search(search_base=self.searchbase, search_filter=user_filter, search_scope=SUBTREE, attributes=attributes)
     
        if self.connection.entries:
            user_dn = self.connection.entries[0]     
            return user_dn.entry_dn
        return None 
            
    def get_user_phone(self, username):
        try:
            if not self.connection:
                if not self.connect():
                    return False
            parts = username.split('\\')
            # 取最后一个元素
            username = parts[-1]
            user_filter = f'(&(objectCategory=person)(objectClass=user)(sAMAccountName={username}))'
            attributes = ['*', '+']
            self.connection.search(search_base=self.searchbase, search_filter=user_filter, search_scope=SUBTREE, attributes=attributes)

            if self.connection.entries:
                user_dn = self.connection.entries[0]

                if 'mobile' in user_dn:
                    return user_dn
                else:
                    print("Phone number not found for user.")
                    return None
            
        except Exception as e:
           print(f'Error: {e}')
        return None

    def authenticate_user(self, username, password):
         
        user_conn=None
        try:
            if not self.connection:
                if not self.connect():
                    return False
            parts = username.split('\\')
            # 取最后一个元素
            username = parts[-1]
            user_filter = f'(&(objectCategory=person)(objectClass=user)(sAMAccountName={username}))'
            self.connection.search(search_base=self.searchbase, search_filter=user_filter, search_scope=SUBTREE)

            if self.connection.entries:
                user_dn = self.connection.entries[0].entry_dn

                user_conn = Connection(self.connection.server, user=user_dn, password=password)

                if user_conn.bind():
                    print(f'User {username} successfully authenticated')
                    
                    return True,username,None
                else:
                    if user_conn.result['description'] == 'invalidCredentials':
                        error_message = user_conn.result['message']
                        if 'data 773' in error_message:  # 773 表示用户必须在下次登录时更改密码
                            return False,username,"773"
                        elif 'data 52e' in error_message:  # 52e 表示无效凭证
                            password_correct = False
                            print(f'Authentication failed for user {username}')
                            return False,username,error_message
                        else:
                            print(f'Authentication failed for user {username}')
                            return False,username,error_message
                    
            else:
                print(f'User {username} not found')
                return False,"",f'User {username} not found'
            return False
        except Exception as e:
            print(f'Error: {e}')
            return False,"",f"{e}"

# 使用示例
# ad = ActiveDirectory(server='ldap://your_ad_server', user='<EMAIL>', password='your_password')
# username_to_authenticate = 'your_username'
# password_to_authenticate = 'your_password'
# ad.authenticate_user(username_to_authenticate, password_to_authenticate)
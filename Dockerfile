# Step 1: Create a base image with system dependencies
FROM harbor.shecc.com/dksz.safe/dksz_safe_adfs:base as base

# Step 2: Create the build stage
FROM base as builder

# Set the working directory in the container
WORKDIR /app


# Copy the rest of the application code into the container
COPY requirements.txt .
RUN pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
COPY . .
WORKDIR /app


# Run ldconfig to update shared library cache
RUN ldconfig 
# Define the command to run the application
CMD ["python", "idp.py", "configs.idp_conf"]

import configparser

class MyConfig:
    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super(MyConfig, cls).__new__(cls)
            cls._instance.config = configparser.ConfigParser()
            cls._instance.config.read('configs/config.ini')
        return cls._instance
    
    def get(self, section, option, fallback=None):
        """
        获取配置文件中的值。
        
        :param section: 配置文件的节
        :param option: 配置项
        :param fallback: 如果配置项不存在，返回的默认值
        :return: 配置项的值
        """
        return self._instance.config.get(section, option, fallback=fallback)
    def __iter__(self):
        """
        支持对 MyConfig 实例进行迭代，返回配置文件中的节
        """
        return iter(self._instance.config.sections())
# 使用方法
# my_config = MyConfig().config

# # 获取MySQL配置
# mysql_host = my_config['MySQL']['host']
# mysql_port = my_config['MySQL']['port']
# mysql_user = my_config['MySQL']['user']
# mysql_password = my_config['MySQL']['password']
# mysql_database = my_config['MySQL']['database']

# # 获取SQLServer配置
# sqlserver_server = my_config['SQLServer']['server']
# sqlserver_port = my_config['SQLServer']['port']
# sqlserver_user = my_config['SQLServer']['user']
# sqlserver_password = my_config['SQLServer']['password']
# sqlserver_database = my_config['SQLServer']['database']

# # 获取Redis配置
# redis_host = my_config['Redis']['host']
# redis_port = my_config['Redis']['port']
# redis_password = my_config['Redis']['password']

# # 获取LDAP配置
# ldap_url = my_config['Ldap']['url']
# ldap_user = my_config['Ldap']['user']
# ldap_password = my_config['Ldap']['password']

# 获取其他配置...

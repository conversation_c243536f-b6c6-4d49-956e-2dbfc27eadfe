# 项目说明：
配合ADFS实现SSO


# 功能：

和ADFS集成，做为ADFS的IDP，提供身份验证。
OA(Sharepoint),mail owa和reporting service等配置与ADFS集成，从而实现与本项目的集成。
支持企业微信扫描登录（与电科数字企业微信集成），同时支持用户名、密码+短信验证码的登录方法。手机号码取自AD，从HR初始化，华讯用户需要从邮箱申请程序提供。


# 安全策略：

## 区分内外网
### 外网登录：
不支持长效会话，超时（30分钟，可配置）或关闭浏览器则会话失效。

### 内网登录：
用户可以选是否保持会话（7天，可配置），如果未选择则和外网登录安全策略一致。如果选择保持会话，则当SSO会话限制时间内用户再次由源系统通过ADFS转发SSO登录请求时，本项目将返回有效登录回复，从而实现用户持续授权访问源系统。

_**Note:** 具体重新身份验证取决于源系统，如源系统会话未过期，不会访问本项目_

## 短信验证码
根据用户名在AD中查找用户手机号，无论用户名是否正确或手机号是否正确，前端均表现一致，提示120秒后可重新发送（防止试探用户名）。多次重复发送验证码受服务商限制，短信服务商的提示信息不提供给前端，仅在后端记录日志。（考虑可以企业微信安全中心中提供一些提醒）

_**Note:** 短信登录方式不支持保持会话（和外网登录类似）_


<!-- 
    配置设置
    ----------------------

    这里是会话、记住和SAML超时以及其他相关配置的设置。

    session_timeout：指定会话因长时间不活动而过期的持续时间（以分钟为单位）。
    ip_whitelist：定义允许访问内网的IP地址或子网的白名单。
    remember_timeout：指定用户会话将被记住的持续时间（以分钟为单位）。
    remember_max_count：指定每个用户记住的会话的最大数量。
    remember_text：向用户显示的文本，指示其记住的会话的有效期。
    saml_timeout：指定SAML会话因长时间不活动而过期的持续时间（以分钟为单位）。
    session_cleartime：指定会话在内存中清除线程启动间隔（以分钟为单位）。

    注意：请根据实际要求替换占位符为实际值。
-->

session_timeout = 30
ip_whitelist = **********/16,***********/16,10.0.0.0/16,********/16
remember_timeout = 10080 
remember_max_count = 10
remember_text = 7天内有效
saml_timeout = 30
session_cleartime = 5

## 问题跟踪：
| 问题描述                                     | 状态   |
|---------------------------------------------|--------|
| redis 修改logingtype                         | 待确认   |    
| redis 增加loc归属地,从caddy获取              | 待确认   |    
| ADFS SAMLRequest请求超时蓝屏（ADFS参数是10分钟） | 待定   |
| SSO SAMLRequest请求超时处理（报未知异常）      | 待定   |
| 前端点击“保留7天”时二维码会刷新                | 待评估   |
| “保留7天”选项的出现是否前端可篡改              | 待评估 |
| SAMLRequest应启用签名验证（防止伪造）          | 待定   |
| 根据SAMLRequest中的时间判定超时               | 待评估 |
| ELK日志格式优化                              | 进行中   |
| idp.xml(外网不应该可以访问，ADFS一次性访问)    | 待定   |    
| 相关证书说明及安全评估（数量、用途、周期、更新、存储）   | 待评估   |   
| 建议配置HTTPS = false，caddy代理(此项目HTTP无证书更新维护工作)  | 待评估   |     
import base64
import urllib.parse
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives import serialization
from cryptography import x509
from cryptography.hazmat.backends import default_backend
 
from services.ldapauthenticator import ActiveDirectory

activate= ActiveDirectory() 
rv= activate.user_reset_password("gmd","T000000!","Thywc00!")
#my_pass= encrypt_password_from_default("23asdadfksss")
my_pass = "Jz1jMX1kMKndelb1tSq198PXLMX2tHN8TOsJHN8VafabWhfLOWa60SSKkppFYJ3/EnKM93Ds9oz3NlCncXA+hlKuqqr7RuJ5TpCRMyvMyuDFLYDvrJiwyjSPg3sV66gsa1yt5PI8TNWgaeZcgWUy62XM1P8qMzAMBdP4uW07Rb3KT+u5NMIedKbw8CRQ5chfWtIY6KFF5W1eLSQrtT/mtzcmV80CpA4ZEmj7EOYoykepjqREkOyDvSWsHx2mqo5eOKBXaehaMJ78hV2NbFUjlxsT2vPt2kolxl+Ih0jI1ScAqLjNMEPzsHk05LA3vayXqlVQJ1ZMMVlgr5UNrMZ08A=="
print(my_pass)
fx= decrypt_password_from_default(my_pass)
print(fx)
# # 私钥证书
# private_key_pem = """
# -----BEGIN PRIVATE KEY-----
# MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDkpD2uOvUOLYp9
# VitQXY1Ty0AAxrcNqphAgmBJ1M6jX/NnNrUP1go/nIg9FrxzYveRvAOZO5qs2WkF
# o/gXvPi+All5eRN1N/IqrbfWC/ICb4eOxm4dhNIoxlVi9WQ1OSB+raKk6sQzuutz
# DTCdbi/nc1/wGvyN6tZ45RwsNnEqzPzLAG0NtOkmX5BdcmrDDhQSoaLfm0/Bp2Ja
# HnTZ2MFF3qlcDcTkSmirqTqgqvBf/Xs5Ip0ppgFUCidvEQE5ZjiN/frq5cVwYQ1K
# bODs4WdX6NkjRpYo/9eBiT2NF/defKCmm58TcRnh3iQKCwu99oLsUyzkjRZeGk1Y
# iaj2j/rfAgMBAAECggEAH3TLOaPJ4Dv6wDixN9rPHVaqxRCR0bMFRGbLiYzaKxcB
# 3VLlATtA6LSH9+OfZL6acfXshtHw+1PkA48mDFoG3Nlk5vX169wZGK8SykOVdalT
# fJynPK9v3IVA7R72vO3RnywTYt4bxUQzrX4EkLw+ofi5G8cStRG4lSRsRlbtJeaQ
# USs4pdaEZPaC7c6GpYIstn3u+SO2SZh4ei1VBPFKRI9nuZEGrG51N7l2zVxaf2KZ
# SA+a7gNybK7ivFKvO+2WjZhI3/Q4OhbIytgqq3qLhDRgpc+QFqUw3BZAZSMICEpN
# z1KwLDcfr/Q7HsJIspFJUDCcRkbUSmay2CjprysOKQKBgQD2+FLzD7hpeNELQp/X
# 53F5rFXcHtUUtU4/og3Heq8+x3+na6i+T6z5BiLHYvjvQtD1e2FAfr+1ed3vzCuY
# v/LfJecjrR4MEEtv4N/NUda0PxNfLdIsOaHCF0g/TtTJ1Der0Uuwi06D9Eul2wtk
# BrDHaXC7s7uBtiIRUkoVSco0+QKBgQDtAFwjNYgGzEQJGy3B1QP83BPjDuQH0dL6
# wKUirmklM1M7F/6xVWVFkFp/e6vnbR/IoRFqIurwsdxk+RkCeLepcIbAEP0N2R7x
# 9hDx1ClDnxN9VQggylJbggslwSACVPtC1nZRgqlA56BYpi2uMY8SykeFI7oR43AW
# AtHb39uclwKBgQCJ9hpQUV3Q/X65bZRhRZbsQ0sLVeXuVYHKpjpQTsaKqWM0IT34
# YgXcboMLIsywy4BzQWMIZkWtmNBT3pgUElYYNdIy6cg5MPkTHGmpBqiLtBicOLqJ
# ngAwrlTbmKvhuz0GLDq9+nP33oZLtltcmygxnUMud66tPRM23eMolTnx8QKBgBt6
# BTSXyjNvjxu3KPDl71ZrdWrHmSxG6TzlBMAmlE4ZyzZ4q7CHg/wypitNfTqRbjPm
# aIxzi+Vn2XNjZcDhmfls8FJoJc5rM6k9q+09eaM7WBkZuJ2El1PAmeZAo6NhktfG
# 4WTFzzWVJQPiED23xSyagvwuji3wvlfu78GcDGv1AoGAB8BKqgo20QJ9v/xgl8KC
# NnXMPXxmXCPz16KGbVSxVBJah5InlnNffrqHln73uItJY3rFLC4WxgmxDjQ2WIB5
# 6JAtNfJ8A0KCD0fAPTcNIFO9CI7IXr0kflhu99hhDHTqOi9Am+KVkqkkyXp1JGNY
# fSFShzcT6LWGiPZdepckucY=
# -----END PRIVATE KEY-----
# """

# # 公钥证书
# cert_pem = """
# -----BEGIN CERTIFICATE-----
# MIIDEzCCAfugAwIBAgIUZVweOXOJz0tLrT9EGtB7g+6FZcEwDQYJKoZIhvcNAQEL
# BQAwGTEXMBUGA1UEAwwOYWRmcy5zaGVjYy5jb20wHhcNMjQwNjEwMDQzNTUxWhcN
# MjUwNjEwMDQzNTUxWjAZMRcwFQYDVQQDDA5hZGZzLnNoZWNjLmNvbTCCASIwDQYJ
# KoZIhvcNAQEBBQADggEPADCCAQoCggEBAOSkPa469Q4tin1WK1BdjVPLQADGtw2q
# mECCYEnUzqNf82c2tQ/WCj+ciD0WvHNi95G8A5k7mqzZaQWj+Be8+L4CWXl5E3U3
# 8iqtt9YL8gJvh47Gbh2E0ijGVWL1ZDU5IH6toqTqxDO663MNMJ1uL+dzX/Aa/I3q
# 1njlHCw2cSrM/MsAbQ206SZfkF1yasMOFBKhot+bT8GnYloedNnYwUXeqVwNxORK
# aKupOqCq8F/9ezkinSmmAVQKJ28RATlmOI39+urlxXBhDUps4OzhZ1fo2SNGlij/
# 14GJPY0X9158oKabnxNxGeHeJAoLC732guxTLOSNFl4aTViJqPaP+t8CAwEAAaNT
# MFEwHQYDVR0OBBYEFHdDpQHyxv9p7s+cyDvZgI/T3NRoMB8GA1UdIwQYMBaAFHdD
# pQHyxv9p7s+cyDvZgI/T3NRoMA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZIhvcNAQEL
# BQADggEBAH+5q5masmMh11aa8aJz7j1nGTsdICt3OIZxqW7TJ85BRirf7c4zA1sv
# XHuu5gOmPqqJZBHN4wHh5zjWW5RMLVdver780mqhuY1DE2kk3/OjXRT9OgJU6TMZ
# dCnBFnSujOELv3ISk6GPX5IivyB4SDg8pH7vaQmhHebpULQP0UMXaJY0Q5Ns8EE+
# ycmjSlS7AW+zmkajNF4LiD++LJYt4LyUzClCLKQPqYuweUG7QQ3N9ce4sJ1jiRA5
# YEp9rbf7SSNCWZ7lLGJnqSH36azfCXZpkUkOmhizQ6radBXtcRCr18SLTXc39+x0
# 0/3irY4yZvV7XxaDI467GeLwnU4iEtQ=
# -----END CERTIFICATE-----
# """

# # URL解码参数
# saml_request = urllib.parse.unquote(
#     "fZFBS8QwEIX/Ssm9TdqN7RLawrKLUFARFQ/eYjJlA21SM6nVf2+2PYgIe5zhvTfz8WqU4zCJwxzO9gk+ZsCQdKeGGJ2W8F5oVcg07zlLuQaW7vObfbqvyqLqNdtxpknyCh6Nsw0pMkaSDnGGzmKQNsQVK3jKyjRnL6wSPBd5mfGKvZHkFO8YK8PqPIcwoaB0WZYMz6BUptwoON9RREc9aONBBZIcnUW45M7eCifRoLByBBRBiefD/Z2ILwi1icRscQJlegPxya9xsChW1OvuybvglBtIW68ofrNeN0lE8BcU0l5QIonUPf6irCONmk+jAGnwM4aabvltvRXwEFO706MbjPpObp0f5RXOPMvXTSypX6V/aQ/D4JajBxmgIfEakIS2Nf3fdPsD"
# )
# relay_state = urllib.parse.unquote("e1d72058-b5a0-4ed0-89c4-7e5d90d2bfb7")
# sigalg = urllib.parse.unquote("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256")
# signature1 = urllib.parse.unquote(
#     "XvMZArHyGOF/dRJ2HO7v9GORoakRsGv6m7HkLKZAXeUcSx6AU8FWP+uvXINOMj0WAJ1XoBiXccCyFla27m1327XlPJ7XBmHH3T9DHvZSksTAhHfYnD1iPJxocFl4ZXYKNvhDAnMXFjHMMGONm/zebUfSMjq8auh5QCo9yprryO48XufCSngqMlpRcS7il9GJaNW9H4uG7KQKx5kIZyjq5b2cchWLTPBHoix6SMOCz830pukboXfxuiefbyOWrEoK4mcqO6oJyXOiZsNIdvlFpUcWkk8qwAwFS4dUY04K78DhbfyJxmQ8PYZSovUus0houbR3i0rwsqXRPlMtrvKhpA=="
# )

# # 构建待签名字符串
# query_string = f"SAMLRequest={urllib.parse.quote(saml_request)}&RelayState={urllib.parse.quote(relay_state)}&SigAlg={urllib.parse.quote(sigalg)}"

# # 加载私钥
# private_key = serialization.load_pem_private_key(
#     private_key_pem.encode(), password=None, backend=default_backend()
# )

# # 使用私钥签名
# signature = private_key.sign(
#     query_string.encode("ascii"), padding.PKCS1v15(), hashes.SHA256()
# )

# # Base64编码和URL编码签名
# encoded_signature = urllib.parse.quote(base64.b64encode(signature).decode("ascii"))

# # 打印生成的签名
# print(f"Generated Signature: {encoded_signature}")

# # Base64解码和URL解码签名
# decoded_signature = base64.b64decode(urllib.parse.unquote(signature1))

# # 解析公钥证书并获取公钥
# cert = x509.load_pem_x509_certificate(cert_pem.encode(), default_backend())
# public_key = cert.public_key()

# # 验证签名
# try:
#     public_key.verify(
#         decoded_signature,
#         query_string.encode("ascii"),
#         padding.PKCS1v15(),
#         hashes.SHA256(),
#     )
#     print("Signature is valid.")
# except Exception as e:
#     print(f"Signature verification failed: {e}")

